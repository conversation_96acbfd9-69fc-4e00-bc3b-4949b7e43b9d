
# 📄 SRS – <PERSON><PERSON><PERSON>n lý thông tin Cộng tác viên (CTV) trên App Sale

 
## 1. <PERSON><PERSON><PERSON><PERSON> thiệu

### 1.1 M<PERSON><PERSON> đích
Tài liệu này mô tả chi tiết yêu cầu hệ thống cho chức năng quản lý thông tin Cộng Tác <PERSON> (CTV) trên màn hình trang chủ ( Homepage) ứng dụng App Sale của KienlongBank. Chức năng này cho phép CTV, tổ trưởng, trưởng nhóm CTV theo dõi thông tin hạn mức, dư nợ, tài sản bảo đảm, tình trạng chậm trả và số lượng khách hàng thông qua dữ liệu đồng bộ từ hệ thống Mango.

### 1.2 Phạm vi
Tính năng là một phần của hệ thống App Sale – nền tảng phục vụ đội ngũ bán hàng và cộng tác viên giới thiệu sản phẩm vay.

### 1.3 Định nghĩa và từ viết tắt
- CTV: Cộng Tác Viên
- KLB: KienlongBank
- TSBĐ: Tài sản bảo đảm
- CIF: Customer Information File – Mã định danh khách hàng
- UI/UX: Giao diện người dùng / Trải nghiệm người dùng

### 1.4 Vai trò sử dụng
- CTV, Tổ trưởng CTV, Trưởng nhóm CTV
- Hệ thống back-office hỗ trợ phân quyền
Chức năng **Quản lý thông tin CTV** cho phép các cộng tác viên (CTV), tổ trưởng, trưởng nhóm CTV theo dõi tổng quan và chi tiết các thông tin về hạn mức bảo lãnh, dư nợ, tài sản đảm bảo, số lượng khách hàng, và tình trạng chậm trả được đồng bộ từ hệ thống Mango. Qua đó hỗ trợ việc quản lý công việc, đánh giá hiệu suất và ra quyết định kịp thời.

---

## 2. Luồng nghiệp vụ chính

### Khi CTV, Tổ trưởng hoặc Trưởng nhóm CTV đăng nhập vào App Sale, hệ thống hiển thị màn hình Homepage với các khu vực chính:

### 📋 2.1. Màn hình Homepage – Bố cục 6 phần chính trải dài dạng cuộn

| Phần               | Mô tả chi tiết |
|--------------------|---------------|
| 1. Thao tác nhanh   | - Text chào cá nhân theo thời gian trong ngày <br> - Button "Thông báo" (hiển thị số thông báo chưa đọc) <br> - Button "Tìm kiếm" khách hàng theo giấy tờ tùy thân <br> - Button "Tạo khoản vay" nhanh <br> - Button "Thông tin sản phẩm" (mở folder "vay trả góp ngày") <br> - Button "Giới thiệu CTV mới" mở màn hình nhập liệu CTV |
| 2. Chức năng        | Danh sách module:<br> - Vay trả góp (danh sách khoản vay)<br> - Trợ lý bán hàng (Thư viện điện tử)<br> - Thu tiền khách hàng (danh sách khách cần thu tiền)<br> - Báo cáo cá nhân hoặc nhóm |
| 3. Thông tin CTV (Tổng quan và Báo cáo nhanh)    | Hiển thị thông tin tài chính và hạn mức CTV theo dữ liệu Mango:<br> - Hạn mức được cấp<br> - Hạn mức bảo lãnh tín chấp còn lại (VNĐ)<br> - Hạn mức bảo lãnh TSBĐ còn lại (VNĐ)<br> - Dư nợ tín chấp / Hạn mức tín chấp (VNĐ)<br> - Dư nợ có TSBĐ / Hạn mức TSBĐ (VNĐ)<br> - Tổng dư nợ hiện tại (VNĐ)<br> - Tổng dư nợ bình quân tháng (VNĐ)<br> - Giá trị TSBĐ (VNĐ)<br> - Tổng số khách hàng<br> - Số tiền và số ngày chậm trả (VNĐ/ngày) |
| 4. Thông tin sản phẩm| Hiển thị điều kiện sản phẩm, hồ sơ cần nộp liên quan đến "vay trả góp ngày" |
| 5. Đã xem gần đây   | 3 tab hiển thị:<br> - Tất cả (khách hàng và khoản vay xem trong 1 tuần)<br> - Khách hàng đã xem gần đây<br> - Khoản vay đã xem gần đây |
| 6. Hướng dẫn sử dụng | Hiển thị hướng dẫn cơ bản cho người dùng về chức năng App Sale |

---

### 🧭 Bố cục thao tác nhanh – Ví dụ giao diện

```
[Chào buổi sáng, Nguyễn Văn A]             [Chuông Thông báo (3)]
[Tìm kiếm] 
[Tạo khoản vay] [Thông tin sản phẩm] [Giới thiệu CTV mới]

[Chức năng]
*có các icon là button cho từng mục*
- Vay trả góp
- Trợ lý bán hàng
- Thu tiền khách hàng
- Báo cáo

[Tổng quan] 
*Gồm 3 Tab ngang hàng nhau*
Hạn mức
- Hạn mức được cấp: 10.000.000.000 VNĐ
- Hạn mức tín chấp còn lại: 3.000.000.000 VNĐ
- Giá trị TSĐB: 8.000.000.000 VNĐ
Có biểu đồ vòng tròn hiển thị màu sắc cho HMBL tín chấp còn lại /HMBL tín chấp và HMBL TSĐB còn lại / HMBL TSĐB
Dư nợ
- Dư nợ có TSĐB 5.000.000.000 VNĐ
- Dư nợ không TSĐB : 1.000.000.000 VNĐ
Có biểu đồ vòng tròn hiển thị Tổng dư nợ, và chia màu sắc cho phần  tỉ lệ của Dư nợ Có TSĐB và Dư nợ không TSĐB (Tổng dư nợ= Dư nợ Có TSĐB+ Dư nợ không TSĐB)
Số tiền nộp
- Số tiền gốc /ngày : 2.000.000 VNĐ
- Số tiền lãi/ ngày : 500.000 VNĐ
- Số tiền nộp/ ngày : 2.500.000 VNĐ

[Báo cáo nhanh] 
- Tổng khách hàng: 120
- Số tiền gốc chậm nộp
- Số ngày chậm nộp
- Bình quân dư nợ tháng

[Thông tin sản phẩm]
- Vay trả góp ngày
- Hồ sơ cần nộp: CCCD, Giấy tờ tài sản, Hợp đồng

[Đã xem gần đây]
- Tab: Tất cả | Khách hàng | Khoản vay

[Hướng dẫn sử dụng]
- Xem hướng dẫn sử dụng App Sale
```
---

## 🖼️ Giao diện người dùng (UI) – Bố cục tổng thể

+------------------------------------------------------+
| [Logo KienlongBank]                                  |
| Chào buổi sáng, Nguyễn Văn A                         |
| [Thông báo (3)] [Tìm kiếm] [Tạo khoản vay] [Giới thiệu CTV mới] [Thông tin sản phẩm] |
+------------------------------------------------------+
| [Module chức năng: Vay trả góp | Trợ lý bán hàng | Thu tiền khách hàng | Báo cáo]   |
+------------------------------------------------------+
| [TỔNG QUAN]                                          |
|------------------------------------------------------|
| [Tab: Hạn mức]                                       |
| - Hạn mức được cấp: 10.000.000.000 VNĐ               |
| - Hạn mức tín chấp còn lại: 3.000.000.000 VNĐ        |
| - Giá trị TSĐB: 8.000.000.000 VNĐ                    |
| (Biểu đồ vòng tròn: HMBL tín chấp còn lại/HMBL tín chấp, HMBL TSĐB còn lại/HMBL TSĐB) |
|------------------------------------------------------|
| [Tab: Dư nợ]                                         |
| - Dư nợ có TSĐB: 5.000.000.000 VNĐ                   |
| - Dư nợ không TSĐB: 1.000.000.000 VNĐ                |
| - Tổng dư nợ hiện tại: 6.000.000.000 VNĐ             |
| - Dư nợ bình quân tháng: 2.000.000.000 VNĐ           |
| (Biểu đồ vòng tròn: Tổng dư nợ, chia màu sắc cho Dư nợ có TSĐB và không TSĐB) |
|------------------------------------------------------|
| [Tab: Số tiền nộp]                                   |
| - Số tiền gốc/ngày: 2.000.000 VNĐ                    |
| - Số tiền lãi/ngày: 500.000 VNĐ                      |
| - Số tiền nộp/ngày: 2.500.000 VNĐ                    |
+------------------------------------------------------+
| [BÁO CÁO NHANH]                                      |
|------------------------------------------------------|
| - Tổng khách hàng: 120                               |
| - Số tiền gốc chậm nộp: 500 triệu VNĐ                |
| - Số ngày chậm nộp: 15 ngày                          |
| - Bình quân dư nợ tháng: 2 tỷ VNĐ                    |
+------------------------------------------------------+
| [Thông tin sản phẩm]                                 |
| - Vay trả góp ngày                                   |
| - Hồ sơ cần nộp: CCCD, TSBĐ,...                      |
+------------------------------------------------------+
| [Đã xem gần đây] [Tất cả | Khách hàng | Khoản vay]   |
| List item ...                                        |
+------------------------------------------------------+
| [Hướng dẫn sử dụng]                                  |
| Text hướng dẫn cơ bản...                             |
+------------------------------------------------------+

...
---

## 📋 2.2.Các trường dữ liệu chính của chức năng quản lý thông tin CTV cần lấy từ Mango( gồm phần Tổng quan và Báo cáo nhanh)

| STT | Tên trường                            | Kiểu dữ liệu       | Bắt buộc | Mô tả chi tiết |
|------|------------------------------------|--------------------|----------|----------------|
| 1    | Số CIF                             | String (12)        | ✅        | Mã định danh CTV trong hệ thống Mango |
| 2    | Họ và tên                         | String             | ✅        | Tên đầy đủ CTV |
| 3    | Hạn mức bảo lãnh được cấp         | Number (VNĐ)       | ✅        | Tổng hạn mức bảo lãnh của CTV |
| 4    | Hạn mức bảo lãnh tín chấp còn lại | Number (VNĐ)       | ✅        | Hạn mức tín chấp còn lại khả dụng |
| 5    | Hạn mức bảo lãnh TSBĐ còn lại      | Number (VNĐ)       | ✅        | Hạn mức bảo lãnh tài sản đảm bảo còn lại |
| 6    | Dư nợ không TSĐB                    | Number (VNĐ)       | ✅        | Số dư nợ tín chấp hiện tại |
| 7    | Hạn mức không TSĐB được cấp         | Number (VNĐ)       | ✅        | Tổng hạn mức tín chấp được cấp |
| 8    | Dư nợ có TSBĐ                    | Number (VNĐ)       | ✅        | Số dư nợ có tài sản đảm bảo hiện tại |
| 9    | Hạn mức TSBĐ được cấp             | Number (VNĐ)       | ✅        | Tổng hạn mức TSBĐ được cấp |
| 10   | Tổng dư nợ hiện tại               | Number (VNĐ)       | ✅        | Tổng dư nợ của CTV |
| 11   | Dư nợ bình quân tháng        | Number (VNĐ)       | ✅        | Trung bình dư nợ hàng tháng |
| 12   | Giá trị tài sản đảm bảo (TSBĐ)    | Number (VNĐ)       | ✅        | Giá trị tổng tài sản đảm bảo hiện có |
| 13   | Tổng số khách hàng               | Number             | ✅        | Số lượng khách hàng do CTV quản lý |
| 14   | Số tiền gốc chậm nộp                  | Number (VNĐ)       | ✅        | Số tiền gốc chậm nộp hiện tại |
| 15   | Số ngày chậm nộp                  | Number             | ✅        | Số ngày chậm trả |

---

## 📡 Quy trình đồng bộ dữ liệu từ Mango

| Bước | Mô tả quy trình |
|-------|----------------|
| 1     | App Sale gửi API lấy dữ liệu tài chính CTV dựa trên Số CIF (12 ký tự) sang hệ thống Mango. Số CIF của CTV không cần hiển thị trên app nhưng là key để gọi các API từ app về Mango để lấy thông tin |
| 2     | Mango trả về thông tin chi tiết về hạn mức, dư nợ, số khách hàng, chậm trả... chi tiết như bảng phía trên |
| 3     | App Sale cập nhật dữ liệu lên giao diện phần “Thông tin Cộng tác viên” trên Homepage. |
| 4     | Hệ thống tự động đồng bộ dữ liệu theo chu kỳ 1 tiếng/lần để đảm bảo dữ liệu luôn mới nhất. |
| 5     | Nếu lỗi trả về hoặc không tìm thấy thông tin, hệ thống hiển thị thông báo phù hợp và không hiển thị dữ liệu. |

---

## ⚠️ Ràng buộc nghiệp vụ và xử lý ngoại lệ

| Mã | Nội dung quy tắc | Hành động / Thông báo |
|-----|------------------|-----------------------|
| BR001 | Chỉ user có vai trò CTV, Tổ trưởng, Trưởng nhóm mới được xem màn hình và dữ liệu | Không cho phép truy cập màn hình với vai trò khác |
| BR002 | Dữ liệu tài chính đồng bộ 1 giờ/lần, không cho phép cập nhật thủ công | Thông báo lỗi nếu cập nhật thất bại |
| BR003 | Nếu API trả về lỗi hoặc không có dữ liệu tương ứng CIF | Hiển thị thông báo “Không tìm thấy thông tin tài chính CTV” |
| BR004 | Thông báo số lượng thông báo chưa đọc trên button “Thông báo” | Cập nhật theo thời gian thực |
| BR005 | Các phần chức năng khác trên Homepage chỉ hiển thị theo phân quyền | Ẩn/hiện tùy theo vai trò |
| BR006 | Button “Tạo khoản vay” chỉ mở màn hình tạo mới khoản vay cho CTV đang hoạt động | Kiểm tra trạng thái trước khi cho phép thao tác |


...


## 📚 Tài liệu tham khảo

- BRD_App_Sale_Full.V2.md  
- Các tài liệu API đồng bộ Mango  
- Bảng phân quyền tính năng theo role  
- Tài liệu đăng nhập App Sale  
