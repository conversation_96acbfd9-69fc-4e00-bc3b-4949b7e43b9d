# SRS – Giới thiệu Cộng tác viên

## 1. <PERSON><PERSON><PERSON><PERSON> thiệu

### 1.1 <PERSON><PERSON><PERSON> đích
Tài liệu này mô tả chi tiết yêu cầu hệ thống cho chức năng Giới thiệu Cộng Tá<PERSON> (CTV) trên ứng dụng App Sale của KienlongBank. Chức năng cho phép người dùng hệ thống thực hiện giới thiệu thêm người dùng làm CTV cho bank, xác minh giấy tờ và gửi yêu cầu phê duyệt thông qua luồng xác thực.

### 1.2 Phạm vi
Là một chức năng của hệ thống App Sale – Sản phẩm: Vay trả góp ngày

### 1.3 Định nghĩa và từ viết tắt
- CTV: Cộng Tác Viên
- KLB: KienlongBank
- QR: Mã QR trên thẻ CCCD
- UI/UX: Giao diện người dùng / Trải nghiệm người dùng

### 1.4 Vai trò sử dụng
- Người dùng app sale với các role CTV/Trưởng nhóm CTV/ Tổ trưởng CTV 

---

## 2. Mô tả màn hình UI/UX
Từ **màn hình Trang chủ**, chọn **Giới thiệu CTV** => Hiển thị màn hình Hướng dẫn xác thực giấy tờ 


### 2.1 Màn hình Hướng dẫn xác thực giấy tờ 
### Bảng mô tả các trường thông tin màn hình Hướng dẫn xác thực giấy tờ

| STT | Trường thông tin          | Kiểu hiển thị | Kiểu thao tác | Mô tả chi tiết                                                                                                                                                 |
| --- | ------------------------- | ------------- | ------------- | -------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| 1   | Nút quay lại              | Button        | Nhấn          | Quay trở lại màn hình Giới thiệu                                                                                                                               |
| 2   | Tiêu đề                   | Label         | Hiển thị      | Hiển thị tiêu đề "Hướng dẫn xác thực giấy tờ"                                                                                                                  |
| 3   | Ảnh minh họa CCCD         | Image         | Hiển thị      | Hiển thị hình ảnh minh họa giấy tờ CCCD hợp lệ                                                                                                                 |
| 4   | Hướng dẫn sử dụng giấy tờ | Text          | Hiển thị      | Hiển thị các hướng dẫn: Giấy tờ còn hạn sử dụng. Là hình gốc, không scan và photocopy./Chụp trong môi trường đủ ánh sáng./Đảm bảo ảnh rõ nét, không bị mờ loá. |
| 5   | Tiêu đề tránh sử dụng     | Text          | Hiển thị      | Hiển thị tiêu đề "Tránh sử dụng"                                                                                                                               |
| 6   | Ảnh minh họa sai 1        | Image         | Hiển thị      | Ảnh minh họa CCCD bị mờ, có dấu X                                                                                                                              |
| 7   | Mô tả ảnh sai 1           | Text          | Hiển thị      | Hiển thị mô tả: "Không chụp quá mờ"                                                                                                                            |
| 8   | Ảnh minh họa sai 2        | Image         | Hiển thị      | Ảnh minh họa CCCD bị mất góc, có dấu X                                                                                                                         |
| 9   | Mô tả ảnh sai 2           | Text          | Hiển thị      | Hiển thị mô tả: "Không mất góc"                                                                                                                                |
| 10  | Ảnh minh họa sai 3        | Image         | Hiển thị      | Ảnh minh họa CCCD bị lóa sáng, có dấu X                                                                                                                        |
| 11  | Mô tả ảnh sai 3           | Text          | Hiển thị      | Hiển thị mô tả: "Không chụp loá sáng"                                                                                                                          |
| 12  | Button Tôi đã hiểu        | Button        | Nhấn          | Nhấn để xác nhận đã đọc hướng dẫn và chuyển sang màn hình tiếp theo                                                                                            |


### 2.2 Màn hình cung cấp giấy tờ tùy thân
### Bảng mô tả các trường thông tin màn hình cung cấp giấy tờ tùy thân

| STT | Trường thông tin               | Kiểu hiển thị | Kiểu thao tác | Mô tả chi tiết                                                                 |
| --- | ------------------------------ | ------------- | ------------- | ------------------------------------------------------------------------------ |
| 1   | Nút quay lại                   | Button        | Nhấn          | Quay trở lại màn hình Hướng dẫn xác thực giấy tờ                               |
| 2   | Tiêu đề                        | Label         | Hiển thị      | Hiển thị tiêu đề "Giới thiệu CTV"                                              |
| 3   | Text cung cấp giấy tờ tùy thân | Text          | Hiển thị      | Hiển thị hướng dẫn "Cung cấp giấy tờ tuỳ thân"                                 |
| 4   | Text hướng dẫn chọn giấy tờ    | Text          | Hiển thị      | Hiển thị hướng dẫn "Vui lòng chọn loại giấy tờ tùy thân để xác thực thông tin" |
| 5   | Dropdown loại giấy tờ          | Dropdown      | Chọn          | Chọn loại giấy tờ: CCCD / Thẻ Căn Cước (mặc định chọn CCCD)                    |
| 6   | Khung upload ảnh mặt trước     | Image + Icon  | Nhấn          | Khung hiển thị và upload ảnh mặt trước giấy tờ, có icon máy ảnh                |
| 7   | Khung upload ảnh mặt sau       | Image + Icon  | Nhấn          | Khung hiển thị và upload ảnh mặt sau giấy tờ, có icon máy ảnh                  |
| 8   | Button Tiếp tục                | Button        | Nhấn          | Nhấn để tiếp tục, chỉ enable khi đã upload đủ 2 mặt ảnh giấy tờ                |


### 2.3 Màn hình chọn loại tải lên giấy tờ
Xuất hiện khi click chọn một khung ảnh giấy tờ
### Bảng mô tả các trường thông tin màn hình chọn loại tải lên giấy tờ

| STT | Trường thông tin            | Kiểu hiển thị | Kiểu thao tác | Mô tả chi tiết                                          |
| --- | --------------------------- | ------------- | ------------- | ------------------------------------------------------- |
| 1   | Button Chụp ảnh             | Button        | Nhấn          | Mở camera để chụp ảnh giấy tờ trực tiếp                 |
| 2   | Button Chọn ảnh từ thư viện | Button        | Nhấn          | Mở thư viện ảnh trên thiết bị để chọn ảnh giấy tờ đã có |
| 3   | Button Huỷ bỏ               | Button        | Nhấn          | Đóng popup, không thực hiện thao tác tải lên giấy tờ    |

### 2.4 Màn hình trước khi chụp ảnh giấy tờ mặt trước 
 | STT | Trường thông tin | Kiểu hiển thị | Kiểu thao tác | Mô tả chi tiết                                                        |
 | --- | ---------------- | ------------- | ------------- | --------------------------------------------------------------------- |
 | 1   | Nút quay lại     | Button        | Nhấn          | Quay trở lại màn hình cung cấp giấy tờ tùy thân                       |
 | 2   | Tiêu đề          | Label         | Hiển thị      | Hiển thị tiêu đề "Chụp mặt trước"                                     |
 | 3   | Khung chụp       | Frame         | Hiển thị      | Khung chụp trống để căn chỉnh giấy tờ trước khi chụp                  |
 | 4   | Text hướng dẫn   | Text          | Hiển thị      | Hiển thị hướng dẫn "Vui lòng đặt giấy tờ vào trong khung để xác thực" |
 | 5   | Button chụp ảnh  | Button        | Nhấn          | Nhấn để chụp ảnh mặt trước giấy tờ                                    |

### 2.5 Màn hình sau khi chụp ảnh giấy tờ mặt trước 
 | STT | Trường thông tin   | Kiểu hiển thị | Kiểu thao tác | Mô tả chi tiết                                              |
 | --- | ------------------ | ------------- | ------------- | ----------------------------------------------------------- |
 | 1   | Nút quay lại       | Button        | Nhấn          | Quay trở lại màn hình cung cấp giấy tờ tùy thân             |
 | 2   | Tiêu đề            | Label         | Hiển thị      | Hiển thị tiêu đề "Chụp mặt trước"                           |
 | 3   | Khung chụp         | Frame/Image   | Hiển thị      | Hiển thị ảnh mặt trước giấy tờ vừa chụp                     |
 | 4   | Text kiểm tra      | Text          | Hiển thị      | Hiển thị hướng dẫn "Kiểm tra mặt trước GTTT"                |
 | 5   | Button Chụp lại    | Button        | Nhấn          | Nhấn để chụp lại ảnh mặt trước giấy tờ nếu chưa đạt yêu cầu |
 | 6   | Button Lấy ảnh này | Button        | Nhấn          | Nhấn để xác nhận sử dụng ảnh mặt trước giấy tờ vừa chụp     |

### 2.6 Màn hình trước khi chụp ảnh giấy tờ mặt sau
| STT | Trường thông tin | Kiểu hiển thị | Kiểu thao tác | Mô tả chi tiết                                                        |
| --- | ---------------- | ------------- | ------------- | --------------------------------------------------------------------- |
| 1   | Nút quay lại     | Button        | Nhấn          | Quay trở lại màn hình cung cấp giấy tờ tùy thân                       |
| 2   | Tiêu đề          | Label         | Hiển thị      | Hiển thị tiêu đề "Chụp mặt sau"                                       |
| 3   | Khung chụp       | Frame         | Hiển thị      | Khung chụp trống để căn chỉnh giấy tờ mặt sau trước khi chụp          |
| 4   | Text hướng dẫn   | Text          | Hiển thị      | Hiển thị hướng dẫn "Vui lòng đặt giấy tờ vào trong khung để xác thực" |
| 5   | Button chụp ảnh  | Button        | Nhấn          | Nhấn để chụp ảnh mặt sau giấy tờ                                      |

### 2.7 Màn hình sau khi chụp ảnh giấy tờ mặt sau 
| STT | Trường thông tin   | Kiểu hiển thị | Kiểu thao tác | Mô tả chi tiết                                            |
| --- | ------------------ | ------------- | ------------- | --------------------------------------------------------- |
| 1   | Nút quay lại       | Button        | Nhấn          | Quay trở lại màn hình cung cấp giấy tờ tùy thân           |
| 2   | Tiêu đề            | Label         | Hiển thị      | Hiển thị tiêu đề "Chụp mặt sau"                           |
| 3   | Khung chụp         | Frame/Image   | Hiển thị      | Hiển thị ảnh mặt sau giấy tờ vừa chụp                     |
| 4   | Text kiểm tra      | Text          | Hiển thị      | Hiển thị hướng dẫn "Kiểm tra mặt sau GTTT"                |
| 5   | Button Chụp lại    | Button        | Nhấn          | Nhấn để chụp lại ảnh mặt sau giấy tờ nếu chưa đạt yêu cầu |
| 6   | Button Lấy ảnh này | Button        | Nhấn          | Nhấn để xác nhận sử dụng ảnh mặt sau giấy tờ vừa chụp     |

### 2.8 Màn hình quét mã QR CCCD

 | STT | Trường thông tin              | Kiểu hiển thị | Kiểu thao tác | Mô tả chi tiết                                                                                                                         |
 | --- | ----------------------------- | ------------- | ------------- | -------------------------------------------------------------------------------------------------------------------------------------- |
 | 1   | Nút quay lại                  | Button        | Nhấn          | Quay trở lại màn hình cung cấp giấy tờ tùy thân                                                                                        |
 | 2   | Bỏ qua                        | Button        | Nhấn          | Bắt buộc quét QR nếu quét mã QR lỗi hoặc mờ không quét được thì hiển thị button Bỏ qua -> chuyển sang nhập cung cấp thông tin bằng tay |
 | 3   | Text hướng dẫn                | Text          | Hiển thị      | Hiển thị hướng dẫn “Vui lòng đưa QR trên CCCD vào khung hình”                                                                          |
 | 4   | Camera view                   | Camera        | Hiển thị      | Hiển thị khung camera để quét mã QR trên CCCD                                                                                          |
 | 5   | Logo KienlongBank             | Image         | Hiển thị      | Hiển thị logo thương hiệu KienlongBank                                                                                                 |
 | 6   | Button bật/tắt đèn flash      | Button        | Nhấn          | Bật hoặc tắt đèn flash khi quét mã QR                                                                                                  |
 | 7   | Button tải ảnh QR từ thư viện | Button        | Nhấn          | Mở thư viện ảnh trên thiết bị để chọn ảnh mã QR đã có                                                                                  |


### 2.9 Màn hình xác nhận thông tin cá nhân
- Các field read-only hiển thị thông tin 

| STT | Trường thông tin    | Kiểu hiển thị       | Kiểu thao tác   | Bắt buộc nhập | Mô tả chi tiết                                                       | Thông báo khi vi phạm quy tắc (Mess)                                   |
| --- | ------------------- | ------------------- | --------------- | ------------- | -------------------------------------------------------------------- | ---------------------------------------------------------------------- |
| 1   | Họ và tên           | Textbox             | Nhập/chỉnh sửa  | Có            | Chuỗi ký tự không chứa số, độ dài 3–50 ký tự. QR                     | "Họ và tên tối đa 50 ký tự không chứa số, ký tự đặc biệt."             |
| 2   | Số giấy tờ          | Textbox             | Nhập/chỉnh sửa  | Có            | Số định danh cá nhân, định dạng 9 hoặc 12 chữ số. QR                 | "Số giấy tờ không hợp lệ hoặc đã tồn tại trên hệ thống."               |
| 3   | Địa chỉ cư trú      | Textbox             | Nhập/chỉnh sửa  | Có            | Địa chỉ lấy từ OCR/QR, tối đa 255 ký tự. QR                          | "Địa chỉ cư trú tối đa 255 ký tự."                                     |
| 4   | Số điện thoại       | Textbox             | Nhập            | Có            | 10 chữ số, bắt đầu bằng số 0, chỉ nhận ký tự số.                     | "Số điện thoại không hợp lệ. Vui lòng nhập đúng 10 số bắt đầu bằng 0." |
| 5   | Chức danh           | Textbox (read-only) | Không chỉnh sửa | Có            | Mặc định là "Cộng tác viên".                                         | -                                                                      |
| 6   | Tỉnh/Thành phố      | Textbox (read-only) | Không chỉnh sửa | Có            | Mặc định Hiển thị địa chỉ Tỉnh/Thành phố đang làm của CTV giới thiệu |                                                                        |
| 7   | Chi nhánh đăng ký   | Textbox (read-only) | Không chỉnh sửa | Có            | Mặc định Hiển thị tên chi nhánh của CTV giới thiệu                   |                                                                        |
| 8   | Địa chỉ chi nhánh   | Textbox (read-only) | Không chỉnh sửa | Có            | Hiển thị địa chỉ ứng với chi nhánh                                   | -                                                                      |
| 9   | Mã người giới thiệu | Textbox (read-only) | Không chỉnh sửa | Có            | Hiển thị mã giới thiệu của CTV đang giới thiệu người mới             |                                                                        |
| 10  | Xác nhận            | Button              | Nhấn            | -             | Gửi thông tin đăng ký.                                               | "Vui lòng nhập đầy đủ các trường bắt buộc trước khi xác nhận."         |
| 11  | Huỷ                 | Button              | Nhấn            | -             | Hủy thao tác đăng ký, quay lại màn hình trước.                       |
  
### 2.10 Màn hình thông báo sau gửi thành công

  | STT | Trường thông tin        | Kiểu hiển thị       | Kiểu thao tác | Mô tả chi tiết                                                                             |
  | --- | ----------------------- | ------------------- | ------------- | ------------------------------------------------------------------------------------------ |
  | 1   | Tiêu đề thông báo       | Text                | Hiển thị      | Hiển thị tiêu đề “Giới thiệu CTV thành công”                                               |
  | 2   | Thông báo hướng dẫn     | Text                | Hiển thị      | Hiển thị nội dung: “Kienlongbank sẽ liên hệ trong thời gian sớm nhất để thông báo kết quả” |
  | 3   | Họ tên                  | Textbox (read-only) | Hiển thị      | Hiển thị họ tên người được giới thiệu                                                      |
  | 4   | Chi nhánh đăng ký       | Textbox (read-only) | Hiển thị      | Hiển thị chi nhánh mà người dùng đã đăng ký                                                |
  | 5   | Chức danh               | Textbox (read-only) | Hiển thị      | Cộng tác viên                                                                              |
  | 6   | Mã Người giới thiệu     | Textbox (read-only) | Hiển thị      | Mã CTV giới thiệu                                                                          |
  | 7   | Họ tên Người giới thiệu | Textbox (read-only) | Hiển thị      | Họ tên CTV giới thiệu                                                                      |
  | 8   | Button trang chủ        | Button              | Nhấn          | Nhấn để quay về màn hình chính hoặc đăng nhập                                              |

---


## 3. Luồng chức năng

### 3.1 Mô tả luồng xử lý chi tiết
1. Từ **màn hình Trang chủ**, chọn **Giới thiệu CTV** => Hiển thị màn hình **Hướng dẫn xác thực giấy tờ**
2. Tại màn hình **Hướng dẫn xác thực giấy tờ** 
  - Nhấn **Tôi đã hiểu** -> sang màn **Cung cấp thông tin giấy tờ**
3. Tại màn hình **Cung cấp giấy tờ tùy thân**:
   - Chọn loại giấy tờ (CCCD/Thẻ căn cước), upload đủ 2 mặt → nút Tiếp tục bật
   - Nhấn **Tiếp tục** -> sang màn hình **Quét mã QR**
4. Tại màn hình **Quét mã QR**:
   - Người dùng đưa CCCD vào camera hoặc tải ảnh QR lên
   - Nếu trường hợp mã QR của thẻ bị mờ lỗi không đọc được QR => Nhấn **Bỏ qua** để thực hiện nhập cung cấp thông tin
   - Hệ thống đọc và đổ thông tin → sang màn hình **Xác nhận thông tin cá nhân**
5. Tại màn hình **Xác nhận thông tin cá nhân**:
   - Hiển thị thông tin đã quét, người dùng nhập bổ sung
   - Khi nhập đủ và đúng định dạng → nút **Xác nhận** bật
   - Nhấn **Xác nhận** → gửi yêu cầu
6. Hệ thống gửi:
   - Thông báo + email đến GĐ/PGĐ đơn vị để duyệt
   - Gửi email xác nhận cho người đăng ký
   - Cấu trúc email:
     - **Người nhận**: Giám đốc/Phó giám đốc phụ trách đơn vị đó
     - **CC**: Email người đăng ký
     - **Tiêu đề**: Tiếp nhận yêu cầu đăng ký làm CTV Sale App
     - **Nội dung**:
       Bạn có 1 yêu cầu đăng ký làm CTV.
       Họ và Tên: Nguyễn Van A
       CCCD: 053873467632
       Người giới thiệu: Nguyễn Thị L – 352145 (Họ và tên - Mã người giới thiệu)
       Sau khi hoàn thành quy trình tuyển dụng. Vui lòng truy cập vào app hoặc link “abc” để phê duyệt cho CTV vào App Sale.
   - Hiển thị màn hình xác nhận thông tin khởi tạo khoản vay thành công 

### 3.2 Sơ đồ luồng chi tiết 
```mermaid
flowchart TD
    A[Đăng nhập App Sale] --> B[Chọn chức năng Giới thiệu CTV]
    B --> C[Hiển thị màn hình Hướng dẫn xác thực giấy tờ]
    C -->|Nhấn Tôi đã hiểu| D[Hiển thị màn hình Cung cấp giấy tờ tùy thân]
    D -->|Chọn loại giấy tờ, upload đủ 2 mặt| E[Hiển thị màn hình Quét mã QR CCCD]
    E -->|Quét QR thành công| F[Hiển thị màn hình Xác nhận thông tin cá nhân]
    E -->|QR lỗi hoặc mờ, nhấn Bỏ qua| F
    F -->|Nhập đủ & đúng định dạng| G[Nhấn Xác nhận]
    G --> H[Hệ thống gửi thông báo + email đến GĐ/PGĐ & người đăng ký]
    H --> I[Hiển thị màn hình xác nhận gửi thành công]

    %% Chú thích các bước
    subgraph Ghi chú
        direction LR
        note1[Quy trình cán bộ KLB không thuộc phạm vi tài liệu này]
        note2[Quét QR có thể bỏ qua để nhập tay]
        note3[Hệ thống gửi email xác nhận và thông báo]
    end

    %% Các nhánh phụ
    B -.-> note1
    E -.-> note2
    H -.-> note3
```
---

## 4. Yêu cầu phi chức năng

- Giao diện tuân thủ chuẩn mobile-first
- Sử dụng tone màu thương hiệu: xanh lá – trắng – xám nhạt
- Giao diện bo góc, thân thiện với người dùng phổ thông
- Hỗ trợ tích hợp OCR và quét QR từ CCCD

---

## 5. Ràng buộc nghiệp vụ

| Mã   | Quy tắc nghiệp vụ                                                                                        |
| ---- | -------------------------------------------------------------------------------------------------------- |
| BR01 | Người dùng phải tick xác nhận đã đọc hướng dẫn thì mới được phép Tiếp tục các bước tiếp theo.            |
| BR02 | Bắt buộc phải upload đầy đủ 2 mặt giấy tờ tùy thân (mặt trước và mặt sau) mới được tiếp tục.             |
| BR03 | Nếu chọn loại giấy tờ là CCCD, hệ thống phải cung cấp tùy chọn quét mã QR.                               |
| BR04 | Nếu là CTV đang công tác, bắt buộc phải nhập email KienlongBank hợp lệ.                                  |
| BR05 | Số điện thoại phải đúng 10 số và bắt đầu bằng số 0.                                                      |
| BR06 | Chỉ hiển thị các tỉnh/thành phố có chi nhánh hoặc phòng giao dịch KienlongBank trong danh sách lựa chọn. |
| BR07 | Nút Xác nhận chỉ được bật khi người dùng đã nhập đầy đủ các trường thông tin bắt buộc.                   |
| BR08 | Nếu chọn loại tài khoản nội bộ, hệ thống sẽ chuyển hướng sang quy trình đăng ký riêng cho nội bộ.        |
| BR09 | Người dùng phải tick chấp nhận chính sách CTV thì mới được phép tiếp tục quy trình đăng ký.              |
| BR10 | Phải chụp hoặc upload ảnh mặt trước và mặt sau giấy tờ tùy thân mới được tiếp tục.                       |
| BR11 | Bắt buộc phải thực hiện quét QR hoặc chọn Bỏ qua (không được để trống bước này).                         |
| BR12 | Chỉ hiển thị danh sách tỉnh/huyện khi đăng ký mới CTV.                                                   |
| BR13 | Email KienlongBank chỉ được nhập với domain @kienlongbank.com, không chấp nhận domain khác.              |
| BR14 | Nếu nhập mã người giới thiệu, phải đúng định dạng mã hệ thống quy định.                                  |
| BR15 | Mỗi số giấy tờ chỉ được đăng ký một lần, không được trùng với hồ sơ đang xử lý trên hệ thống.            |

---

## 6. Xử lý ngoại lệ

| Mã lỗi | Tình huống                                    | Phản hồi hệ thống                                                                         |
| ------ | --------------------------------------------- | ----------------------------------------------------------------------------------------- |
| EX001  | Ảnh giấy tờ bị mờ hoặc thiếu góc              | Hiển thị cảnh báo: “Ảnh không hợp lệ”                                                     |
| EX002  | Chưa nhập đủ các trường bắt buộc              | Đánh dấu đỏ trường thiếu, disable nút gửi                                                 |
| EX003  | Tỉnh/thành phố không có chi nhánh             | Ẩn khỏi danh sách lựa chọn tỉnh/thành phố                                                 |
| EX004  | Email sai định dạng                           | Hiển thị cảnh báo tại trường Email                                                        |
| EX005  | Quét mã QR không thành công                   | Hiển thị cảnh báo: “Không đọc được mã QR. Vui lòng thử lại hoặc chọn Bỏ qua để dùng OCR.” |
| EX006  | Upload ảnh lỗi (định dạng không hỗ trợ)       | Hiển thị cảnh báo: “Tệp ảnh không hợp lệ. Vui lòng chọn định dạng JPG hoặc PNG.”          |
| EX007  | Mã người giới thiệu không tồn tại hoặc sai mã | Hiển thị cảnh báo: “Mã CTV người giới thiệu không hợp lệ.”                                |
| EX008  | Mạng yếu, không gửi được yêu cầu              | Hiển thị thông báo: “Kết nối mạng không ổn định. Vui lòng thử lại sau.”                   |
| EX009  | Trùng số giấy tờ với hồ sơ đang xử lý         | Hiển thị thông báo: “Thông tin đã tồn tại trên hệ thống. Vui lòng liên hệ hỗ trợ.”        |
| EX010  | Không chọn loại tài khoản                     | Hiển thị cảnh báo: “Vui lòng chọn loại tài khoản để tiếp tục”                             |
| EX011  | Người dùng không cấp quyền camera             | Hiển thị cảnh báo hướng dẫn mở lại quyền trong cài đặt thiết bị                           |
| EX012  | Ảnh CCCD bị lóa, lệch góc                     | Hiển thị cảnh báo: “Ảnh chưa đủ rõ ràng. Vui lòng chụp lại”                               |
| EX013  | Upload file vượt quá dung lượng quy định      | Hiển thị cảnh báo: “Tệp ảnh vượt quá dung lượng cho phép (tối đa 5MB)”                    |