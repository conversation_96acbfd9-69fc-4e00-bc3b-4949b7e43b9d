# SRS – Luồng khởi tạo khoản vay mới trên app/web sale

## 1. Giới thiệu


## 2. <PERSON><PERSON><PERSON> đích sử dụng

<PERSON>le **"Vay trả góp ngày"** trong ứng dụng **App Sale** cho phép **Cộng tác viên (CTV)** và các bộ backoffice (CBBH, GDV, Trưởng ĐVKD/GĐKV) quản lý các khoản vay trả góp củ<PERSON> kh<PERSON>ch hàng, bao gồm các chức năng tìm kiếm, lọ<PERSON> tr<PERSON>ng thá<PERSON> hồ s<PERSON>, và tạo mới khoản vay. Màn hình này giúp **CTV** và các bộ backoffice (CBBH, GDV, Trưởng ĐVKD/GĐKV) dễ dàng theo dõi, q<PERSON><PERSON><PERSON> lý, và xử lý các khoản vay từ khi tạo mới đến khi giải ngân.


## 3. <PERSON>uy trình tạo khoản vay mới

<PERSON>hi **CTV** nhấn vào nút **"Tạo khoản vay mới"**, hệ thống sẽ chuyển sang **tiến trình tạo khoản vay mới** với **10 bước**.

- **Bước 1**: Cung cấp giấy tờ tùy thân (người vay chính)
- **Bước 2**: Xác nhận thông tin người vay chính
- **Bước 3**: Cung cấp giấy tờ tùy thân (người đồng vay)
- **Bước 4**: Xác nhận thông tin người đồng vay
- **Bước 5**: Cung cấp thông tin đề nghị vay vốn
- **Bước 6**: Cung cấp thông tin tình hình tài chính
- **Bước 7**: Cung cấp thông tin tài sản bảo đảm (nếu có)
- **Bước 8**: Chi tiết thông tin tài sản bảo đảm
- **Bước 9**: Cung cấp danh mục chứng từ
- **Bước 10**: Xác nhận thông tin khoản vay
- **Bước 11**: Màn hình khởi tạo khoản vay thành công

Mỗi bước trong quy trình sẽ yêu cầu người dùng nhập thông tin cần thiết và xác nhận trước khi tiếp tục đến bước kế tiếp.
---

# Bước 1: Cung cấp giấy tờ tuỳ thân (người vay chính)
## 1. Mô tả màn hình UI/UX 

### 1.1 Màn hình "Cung cấp thông tin giấy tờ tùy thân"

| **Tên trường**        | **Kiểu dữ liệu** | **Ghi chú**                                                                                                                                                                                        |
| --------------------- | ---------------- | -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **Loại giấy tờ**      | **Dropdown**     | Các lựa chọn: **CCCD**, **CMND**, **Hộ chiếu**                                                                                                                                                     |
| **Mặt trước giấy tờ** | **File upload**  | Tải ảnh mặt trước giấy tờ                                                                                                                                                                          |
| **Mặt sau giấy tờ**   | **File upload**  | Tải ảnh mặt sau giấy tờ                                                                                                                                                                            |
| **Tiếp tục**          | **Button**       | Nút này chỉ bật khi người vay đã cung cấp đầy đủ ảnh giấy tờ tùy thân (mặt trước và mặt sau hoặc thông tin trong hộ chiếu). Nếu thiếu hoặc ảnh không rõ, nút **"Tiếp tục"** sẽ bị **vô hiệu hóa**. |


### 1.2 Màn hình chọn loại tải lên giấy tờ

| **Tên trường**           | **Kiểu dữ liệu**                | **Ghi chú**                       |
| ------------------------ | ------------------------------- | --------------------------------- |
| **Tùy chọn tải lên ảnh** | **Button (Chụp ảnh / Tải ảnh)** | Chụp ảnh hoặc tải ảnh từ thư viện |
| **Huỷ bỏ**               | **Button**                      | Để huỷ bỏ thao tác tải ảnh        |

### 1.3 Màn hình "Quét mã QR"

| **Tên trường** | **Kiểu dữ liệu**         | **Ghi chú**                                              |
| -------------- | ------------------------ | -------------------------------------------------------- |
| **Quét mã QR** | **Camera / File upload** | Quét mã QR hoặc tải ảnh mã QR từ thư viện                |
| **Bỏ qua**     | **Button**               | Cho phép người vay bỏ qua bước quét mã QR và sử dụng OCR |
| **Đèn flash**  | **Button toggle**        | Bật hoặc tắt đèn flash cho camera                        |
| **<-**         | **Button**               | Trở lại trang trước                                      |


## 2. Luồng chức năng

### 2.1 Mô tả luồng xử lý chi tiết

1. **Tại màn hình "Cung cấp giấy tờ tùy thân"**:
   - Người vay chọn loại giấy tờ (**CCCD**, **Thẻ căn cước**, hoặc **Hộ chiếu**).
   - Nếu chọn **CCCD** hoặc **Thẻ căn cước**:
     - Người vay sẽ **chụp ảnh mặt trước** và **mặt sau giấy tờ**.
     
   - Nếu chọn **Hộ chiếu**:
     - Người vay sẽ **chụp ảnh trang thông tin cá nhân** 
   
   - **Nút Tiếp tục**:
     - Nếu người vay đã cung cấp đủ ảnh giấy tờ **CCCD/Thẻ căn cước** (mặt trước và mặt sau) **Hộ chiếu** (1 ảnh thông tin hộ chiếu), nút **"Tiếp tục"** sẽ được bật và chuyển sang bước tiếp theo.
     - Nếu là CCCD => nhấn nút tiếp tục => chuyển sang **màn quét QR**
     - Nếu là Thẻ Căn Cước => Nhấn tiếp tục => Thực hiện OCR căn cước => chuyển sang màn **xác nhận thông tin người vay chính**
     - Nếu là Hộ chiếu => nhấn tiếp tục => chuyển sang màn **xác nhận thông tin người vay chính** => Yêu cầu người dùng nhập thông tin cá nhân (không OCR)

2. **Tại màn hình "Quét mã QR"**:
   - Người vay có thể **quét mã QR** trên **CCCD**.
     - Hệ thống sẽ tự động nhận diện mã QR và điền các thông tin vào các trường liên quan.
   - Người vay cũng có thể **tải ảnh QR** từ thư viện thiết bị của mình.
   - Nếu người vay không muốn quét QR, họ có thể **bỏ qua** và sử dụng **OCR** để nhận diện thông tin giấy tờ.

3. Sau khi hoàn tất quét QR hoặc bỏ qua, hệ thống sẽ **điền thông tin nhận diện vào các trường tương ứng** và chuyển sang màn hình **Xác nhận thông tin cá nhân người vay chính**.



## 3. Quy tắc nghiệp vụ

| Mã   | Quy tắc                                                                        |
| ---- | ------------------------------------------------------------------------------ |
| BR01 | Mặc định loại giấy tờ là **Căn cước công dân (CCCD)**.                         |
| BR02 | Người vay **bắt buộc chọn loại giấy tờ** trước khi tiếp tục.                   |
| BR03 | Nếu chọn **CCCD/Thẻ căn cước** → **phải chụp cả mặt trước và mặt sau**.        |
| BR04 | Nếu chọn **Hộ chiếu** → **chỉ cần chụp trang thông tin cá nhân**.              |
| BR05 | Nút **"Tiếp tục"** chỉ được bật khi **đủ ảnh cần thiết** đã được cung cấp.     |
| BR06 | Ảnh phải rõ nét, không mờ, không thiếu góc → **mới cho phép tiếp tục**.        |
| BR07 | Khi bấm vào **icon camera**, hệ thống hiển thị **tùy chọn chụp hoặc tải ảnh**. |
| BR08 | Mỗi ảnh tải lên không vượt quá **dung lượng tối đa 5MB**.                      |



## 4. Xử lý ngoại lệ

| Mã lỗi | Tình huống                                     | Phản hồi hệ thống                                                                |
| ------ | ---------------------------------------------- | -------------------------------------------------------------------------------- |
| EX001  | Không chọn loại giấy tờ                        | Cảnh báo: “Vui lòng chọn loại giấy tờ để tiếp tục.”                              |
| EX002  | Thiếu ảnh giấy tờ (một mặt hoặc toàn bộ)       | Cảnh báo: “Vui lòng cung cấp đầy đủ ảnh giấy tờ theo yêu cầu.”                   |
| EX003  | Ảnh giấy tờ bị mờ, thiếu góc, lệch sáng        | Cảnh báo: “Ảnh không hợp lệ. Vui lòng chụp lại ảnh rõ ràng.”                     |
| EX004  | Thiết bị không cấp quyền truy cập camera       | Cảnh báo: “Vui lòng cấp quyền truy cập camera trong cài đặt thiết bị.”           |
| EX005  | Lỗi khi tải ảnh lên (mạng yếu, định dạng lỗi…) | Cảnh báo: “Lỗi tải ảnh. Vui lòng thử lại.”                                       |
| EX006  | Dung lượng ảnh vượt quá giới hạn               | Cảnh báo: “Tệp ảnh vượt quá dung lượng cho phép (tối đa 5MB).”                   |
| EX007  | Chụp ảnh thất bại liên tiếp 3 lần              | Cảnh báo: “Thao tác không thành công. Vui lòng kiểm tra lại giấy tờ và thử lại.” |

---
# Bước 2: Xác nhận thông tin người vay chính

## 1. Mô tả màn hình UI/UX 

### 1.1 Màn hình "Xác nhận thông tin người vay chính"
| STT | Trường thông tin                              | Kiểu hiển thị     | Kiểu thao tác  | Bắt buộc nhập | Mô tả chi tiết                                                                                                                                                                            |
| --- | --------------------------------------------- | ----------------- | -------------- | ------------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| 1   | Có người đồng vay                             | toggle            | Bật/tắt        | ✔             | Mặc định là tắt toggle                                                                                                                                                                    |
|     | Thông tin nhận dạng giấy tờ                   | Lable (Read-only) |                |               |
| 2   | Họ và tên                                     | Textbox           | Không sửa được | ✔             | Hiển thị từ giấy tờ (OCR/QR). Tối đa 100 ký tự tính cả khoảng trắng. Cho phép nhập chữ, và dấu nháy đơn, không cho nhập 2 dấu cách liền nhau. Không cho nhập dấu cách ở đầu và cuối chuỗi |
| 3   | Số giấy tờ                                    | Textbox           | Không sửa được | ✔             | Hiển thị từ giấy tờ (OCR/QR).Cho phép chỉnh sửa, nhập số, tối đa 12 ký tự số                                                                                                              |
| 4   | Ngày cấp                                      | Datepicker        | Chọn           | ✔             | Hiển thị từ giấy tờ (OCR/QR). Định dạng dd/mm/yyyy                                                                                                                                        |
| 5   | Ngày hết hạn                                  | Datepicker        | Chọn           | ✔             | Hiển thị từ giấy tờ (OCR/QR). Định dạng dd/mm/yyyy                                                                                                                                        |
| 6   | Nơi cấp                                       | Textbox           | Nhập           | ✔             | Hiển thị từ giấy tờ (OCR/QR). Cho phép nhập nơi cấp. Tối đa 100 ký tự                                                                                                                     |
| 7   | Giới tính                                     | Dropdown          | Chọn           | ✔             | Hiển thị từ giấy tờ (OCR/QR). Cho phép chọn Nam/Nữ/Khác                                                                                                                                   |
| 8   | Địa chỉ thường trú                            | Textbox           | Không sửa được | ✔             | Hiển thị từ giấy tờ (OCR/QR). Cho phép nhập địa chỉ thường trú, Tối đa 200 ký tự                                                                                                          |
|     | Thông tin cá nhân                             | Lable(Read-only)  |                |               |                                                                                                                                                                                           |
| 9   | Tình trạng hôn nhân                           | Dropdown          | Chọn           | ✔             | Độc thân, Đã kết hôn, Ly hôn, Khác. Mặc định chọn Độc thân                                                                                                                                |
| 10  | Số điện thoại                                 | Textbox           | Nhập           | ✔             | Bắt đầu bằng số 0, 10 chữ số                                                                                                                                                              |
| 11  | Địa chỉ hiện tại trùng với địa chỉ thường trú | Checkbox          | Click          | ✔             | Mặc định là không tích. Nếu không tích hiển thị thêm các trường thông tin nhập địa chỉ                                                                                                    |
| 12  | Tỉnh/ thành phố                               | Dropdown          | Chọn           | ✔             | Chọn Tỉnh/thành phố                                                                                                                                                                       |
| 13  | Quận/Huyện                                    | Dropdown          | Nhập           | ✔             | Chọn Quận/Huyện thuộc Tỉnh /thành phố                                                                                                                                                     |
| 14  | Phường/Xã                                     | Dropdown          | Tick           | ✔             | Chọn Phường/xã thuộc Quận/Huyện                                                                                                                                                           |
| 15  | Địa chỉ                                       | Textarea          | Tick           | ✘             | Cho phép nhập địa chỉ cụ thể, tối đa 250 ký, 	Cho phép nhập chữ, số, và các kí tự đặc biệt sau: dấu phẩy ( , ), dấu gạch ngang ( - ), dấu gạch chéo ( / ) tự                              |
| 16  | Lưu nháp                                      | Button            | Click          |               | Click để lưu lại hồ sơ đang tạo                                                                                                                                                           |
| 17  | Tiếp tục                                      | Button            | Click          |               | Click để  tiếp tục, chưa nhập đủ các trường bắt buộc disable button                                                                                                                       |
| 18  | <                                             | Button            | Click          |               | Click để  quay trở lại bước trước đó                                                                                                                                                      |
| 19  | Icon trang chủ                                | Button            | Click          |               | Click để quay trở về trang chủ                                                                                                                                                            |


## 2. Luồng chức năng

### 2.1 Mô tả luồng xử lý chi tiết

**Tại màn hình "Xác nhận thông tin người vay chính"**

1. Người dùng chọn bật/tắt toggle **“Có người đồng vay”**
   - Nếu bật toggle:
     - Sau khi bấm “Tiếp tục”, hệ thống chuyển sang **màn hình cung cấp giấy tờ tùy thân người đồng vay**.
     - Logic tương tự màn hình người vay chính: chọn loại giấy tờ, upload 2 mặt, đọc QR hoặc OCR.
   - Nếu tắt toggle:
     - Hệ thống bỏ qua bước nhập thông tin người đồng vay và chuyển sang bước **cung cấp thông tin đề nghị vay vốn**.


2.  Validate Trường thông tin **Họ tên**
     - Nếu chỉ điền 1 từ và không có dấu cách ở giữa thì hiển thị Error message “Vui lòng nhập họ và tên đầy đủ, có dấu cách ở giữa và chỉ chứa dấu nháy đơn.”

3.  Validate Trường thông tin **Số điện thoại**
      - Trường hợp KH nhập không đúng định dạng, hiển thị cảnh báo: “Vui lòng nhập số điện thoại gồm 10 chữ số, bắt đầu bởi số 0”.

4.  Validate Trường thông tin **Tỉnh/Thành phố**
      - Hiển thị giá trị Thành phố Hà Nội và HCM lên đầu, các giá trị còn lại sắp xếp theo thứ tự A - Z
      - Cho phép người dùng nhập tìm kiếm theo giá trị gần đúng

5.  Validate Trường thông tin **Quận/huyện**
      - Load theo “Tỉnh/thành phố” được chọn
      - Cho phép người dùng nhập tìm kiếm theo giá trị gần đúng
  
6.  Validate Trường thông tin **Phường/Xã**
      - Load theo “Quận/huyện” được chọn
      - Cho phép người dùng nhập tìm kiếm theo giá trị gần đúng
  
7.  Validate Trường thông tin **Địa chỉ**
      - Hiện cảnh báo ‘Địa chỉ cho phép nhập chữ, số, và các kí tự đặc biệt sau: dấu phẩy (,), dấu gạch ngang (-), dấu gạch chéo (/)
  
8. Các **trường thông tin bắt buộc:**
    - Nếu để trống → Hiện cảnh báo viền khung nhập highlight đỏ
    - Hiển thị thông báo tại trường highlight đỏ: “Bắt buộc nhập”.

9. Phân biệt theo **loại giấy tờ**:
   - Nếu người dùng chọn loại giấy tờ là **Hộ chiếu**:
     - Hệ thống không hỗ trợ OCR/QR.
     - Người dùng phải nhập **toàn bộ thông tin thủ công**.
   - Nếu chọn **CCCD/Thẻ căn cước**:
     - Hệ thống sử dụng OCR hoặc quét QR để trích xuất thông tin.
     - Cho phép người dùng **review và chỉnh sửa** thông tin đã nhận diện.

10. Button **Tiếp tục**
    - Enable nếu điền đầy đủ các trường thông tin bắt buộc



## 3. Ràng buộc nghiệp vụ

| Mã   | Quy tắc                                                                                                                |
| ---- | ---------------------------------------------------------------------------------------------------------------------- |
| BR01 | Các trường thông tin định dạng sai sẽ được highlight đỏ và không cho tiếp tục                                          |
| BR02 | Trường số điện thoại phải đúng 10 chữ số, bắt đầu bằng số 0                                                            |
| BR03 | Nếu loại giấy tờ là hộ chiếu → người dùng phải nhập toàn bộ thông tin thủ công. Không sử dụng OCR/QR.                  |
| BR04 | Nếu loại giấy tờ là CCCD/Thẻ căn cước → hệ thống hỗ trợ đọc OCR hoặc QR và cho phép chỉnh sửa thông tin đã trích xuất. |



## 4. Ngoại lệ xử lý

| Mã lỗi | Tình huống                 | Phản hồi hệ thống                                            |
| ------ | -------------------------- | ------------------------------------------------------------ |
| EX001  | Không đọc được mã QR       | Thông báo “Không đọc được QR – vui lòng thử lại hoặc bỏ qua” |
| EX002  | Dữ liệu nhập sai định dạng | Highlight đỏ tại trường lỗi và disable nút “Tiếp tục”        |

---

# Bước 3: Cung cấp giấy tờ tùy thân (người đồng vay)

Tại màn xác nhận thông tin người vay chính nếu tích chọn **Có người đồng vay** => thực hiện tiếp cung cấp thông tin giấy tờ người đồng vay tại bước này

Màn hình cung cấp thông tin giấy tờ người đồng vay => Tương tự như màn hình **Cung thông tin người vay chính.**

---
# Bước 4: Xác nhận thông tin người đồng vay

Tương tự màn hình **Xác nhận thông tin người vay chính** bỏ toggle Có người đồng vay

---

# Bước 5: Cung cấp thông tin đề nghị vay vốn

## 1. Mô tả màn hình UI/UX 

### 1.1 Màn hình "Cung cấp thông tin đề nghị vay vốn"

| STT | Trường thông tin       | Kiểu hiển thị       | Kiểu thao tác     | Bắt buộc nhập | Mô tả chi tiết                                                                                                                                                                                    |
| --- | ---------------------- | ------------------- | ----------------- | ------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| 1   | Hình thức vay vốn      | Radio button        | Chọn 1 giá trị    | ✔             | Có TSĐB / Không TSĐB                                                                                                                                                                              |
|     | Phương án vay vốn      | Lable (read-only)   |                   |               |                                                                                                                                                                                                   |
| 2   | Vốn tự có              | Textbox             | Nhập số           | X             | Số tiền mà khách hàng hiện có để tham gia vào phương án vay. Số tiền mặc định 0 VND, tối đa 1 tỉ, định dạng xxx,xxx                                                                               |
| 3   | Số tiền đề nghị vay    | Textbox             | Nhập số           | ✔             | Số tiền khách hàng muốn vay. Số tiền VND. Tối thiểu Min >= 1.000.000 vnd, tối đa 1 tỉ, định dạng xxx,xxx                                                                                          |
| 4   | Thời hạn vay           | Dropdown            | Click             | ✔             | Thời gian vay tính bằng ngày. Bap gồm (30; 60; 90; 270)                                                                                                                                           |
| 5   | Tổng nhu cầu           | Textbox             | Tự động tính      | ✔             | Tổng nhu cầu vốn = Vốn tự có + Số tiền đề nghị vay. Số tiền VND, định dạng xxx,xxx                                                                                                                |
| 6   | CN/PGD                 | Textbox (read-only) | Không thao tác    | ✔             | Hệ thống tự điền theo mã chi nhánh của CTV ăn theo                                                                                                                                                |
| 7   | Phương thức vay        | Textbox (disabled)  | Không thao tác    | ✔             | Mặc định là “Vay trả góp”                                                                                                                                                                         |
| 8   | Mục đích sử dụng vốn   | Dropdown            | Chọn từ danh sách | ✔             | Bao gồm: Phục vụ nhu cầu đời sống/ Phục vụ hoạt động kinh doanh/ Khác                                                                                                                             |
| 9   | Tên mục đích           | Textbox             | Nhập chữ          | ✔             | Nhập tên mục đích nếu Chọn mục đích sử dụng vốn là **khác** . Maxlength: 100 ký tự, Cho phép nhập chữ, số, và các kí tự đặc biệt sau: dấu phẩy ( , ), dấu gạch ngang ( - ), dấu gạch chéo ( / ) . |
| 10  | Hình thức trả nợ       | Textbox (read-only) | Không thao tác    | ✔             | Mặc định hiển thị “Trả góp nợ gốc và lãi tiền vay hàng ngày”                                                                                                                                      |
| 11  | Phương thức giải ngân  | Radio button        | Chọn 1 giá trị    | ✔             | Nhận tiền mặt / Chuyển khoản.                                                                                                                                                                     |
| 12  | Số tài khoản nhận tiền | Dropdown            | Chọn 1 giá trị    | ✔             | Hiển thị danh sách số tài khoản thanh toánh ứng với CIF KH đã mở tại KLB                                                                                                                          |
| 13  | Lưu nháp               | Button              | Click             |               | Click để lưu nháp lại hồ sơ khách hàng                                                                                                                                                            |
| 14  | Tiếp tục               | Dropdown            | Click             |               | Click để tiếp tục sang bước tiếp                                                                                                                                                                  |
| 15  | <                      | Button              | Click             |               | Click để  quay trở lại bước trước đó                                                                                                                                                              |
| 16  | Icon trang chủ         | Button              | Click             |               | Click để quay trở về trang chủ                                                                                                                                                                    |


## 2. Luồng chức năng

### 2.1 Mô tả luồng xử lý chi tiết

**Tại màn hình "Cung cấp thông tin đề nghị vay vốn"**
1. Validate trường **phương thức giải ngân**
  - Nếu người dùng chọn hình thức giải ngân là **Chuyển khoản** 
      - Hệ thống gọi FCC Lấy Danh sách TKTT theo CIF của khách hàng dựa theo Số CCCD/Thẻ căn cước đã cung cấp ở bước trước.
      - Nếu KH chưa mở CIF tại KLB hiển thị thông báo: "Hệ thống không tìm thấy thông tin tài khoản của người vay. Vui lòng mở tài khoản trước khi chọn hình thức này."
      - Nếu KH đã mở CIF nhưng chưa có TKTT tại KLB: "Hệ thống không tìm thấy thông tin tài khoản của người vay. Vui lòng mở tài khoản trước khi chọn hình thức này."

2. Các **trường thông tin bắt buộc:**
    - Nếu để trống → Hiện cảnh báo viền khung nhập highlight đỏ
    - Hiển thị thông báo tại trường highlight đỏ: “Bắt buộc nhập”.

3. Button **Tiếp tục**
    - Enable nếu điền đầy đủ các trường thông tin bắt buộc
---

# Bước 6: Cung cấp thông tin tình hình tài chính
## 1. Mô tả màn hình UI/UX 

### 1.1 Màn hình "Cung cấp thông tin tình hình tài chính"

| STT | Trường thông tin               | Kiểu hiển thị | Kiểu thao tác  | Bắt buộc nhập | Mô tả chi tiết                                                                                                                                               |
| --- | ------------------------------ | ------------- | -------------- | ------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| 1   | Tình hình tài chính            | Lable         |                |               |                                                                                                                                                              |
| 2   | Nguồn thu                      | Dropdown      | Chọn           | ✔             | Bao gồm: Kinh doanh/Từ lương/Khác . Mặc định: Kinh doanh                                                                                                     |
| 3   | Doanh số bình quân/ ngày       | Textbox       | Nhập số nguyên | ✔             | Đơn vị VNĐ,  chỉ hiển thị nếu Nguồn thu là "Kinh doanh". Số tiền mặc định 0 VND, tối đa 1 tỉ, định dạng xxx,xxx                                              |
| 4   | Thu nhập bình quân/Ngày        | Textbox       | Nhập số nguyên | ✔             | Đơn vị VNĐ, hiển thị với mọi nguồn thu. Số tiền mặc định 0 VND, tối đa 1 tỉ, định dạng xxx,xxx                                                               |
| 5   | Địa điểm sản xuất - kinh doanh | Lable         |                |               | chỉ hiển thị nếu Nguồn thu "Kinh doanh"                                                                                                                      |
| 6   | Tỉnh/ thành phố                | Dropdown      | Chọn           | ✔             | Chọn Tỉnh/thành phố                                                                                                                                          |
| 7   | Quận/Huyện                     | Dropdown      | Nhập           | ✔             | Chọn Quận/Huyện thuộc Tỉnh /thành phố                                                                                                                        |
| 8   | Phường/Xã                      | Dropdown      | Tick           | ✔             | Chọn Phường/xã thuộc Quận/Huyện                                                                                                                              |
| 9   | Địa chỉ                        | Textarea      | Tick           | ✘             | Cho phép nhập địa chỉ cụ thể, tối đa 250 ký, 	Cho phép nhập chữ, số, và các kí tự đặc biệt sau: dấu phẩy ( , ), dấu gạch ngang ( - ), dấu gạch chéo ( / ) tự |
| 10  | Lưu nháp                       | Button        | Click          |               | Click để lưu lại hồ sơ đang tạo                                                                                                                              |
| 11  | Tiếp tục                       | Button        | Click          |               | Click để  tiếp tục, chưa nhập đủ các trường bắt buộc disable button                                                                                          |
| 12  | <                              | Button        | Click          |               | Click để  quay trở lại bước trước đó                                                                                                                         |
| 13  | Icon trang chủ                 | Button        | Click          |               | Click để quay trở về trang chủ                                                                                                                               |

## 2. Luồng chức năng

### 2.1 Mô tả luồng xử lý chi tiết

**Tại màn hình "Cung cấp thông tin tình hình tài chính"**
1. Các **trường thông tin bắt buộc:**
    - Nếu để trống → Hiện cảnh báo viền khung nhập highlight đỏ
    - Hiển thị thông báo tại trường highlight đỏ: “Bắt buộc nhập”.

2. Button **Tiếp tục**
    - Enable nếu điền đầy đủ các trường thông tin bắt buộc

---

# Bước 7: Cung cấp thông tin tài sản bảo đảm (nếu có)

Tại **Bước 5** nếu CTV tích chọn có TSBĐ => Hiển thị màn này để cung cấp thông tin tài sản bảo đảm

## 1. Mô tả màn hình UI/UX 

### 1.1 Màn hình "Cung cấp thông tin tài sản bảo đảm"

| STT | Trường thông tin           | Kiểu hiển thị           | Kiểu thao tác          | Bắt buộc nhập | Mô tả chi tiết                                                                          |
| --- | -------------------------- | ----------------------- | ---------------------- | ------------- | --------------------------------------------------------------------------------------- |
| 1   | Loại tài sản               | Textbox(read-only)      | không thao tác         | Có            | Mặc định là Mô tô/xe máy                                                                |
| 2   | Giá trị tài sản            | Textbox số              | Nhập số nguyên         | Có            | Nhập giá trị tài sản bằng VNĐ, Số tiền mặc định 0 VND, tối đa 1 tỉ, định dạng xxx,xxx   |
| 3   | Giá trị tài sản (bằng chữ) | Textbox                 | Tự động hiển thị       | Không         | Hiển thị tự động giá trị tài sản bằng chữ, không cho phép chỉnh sửa                     |
| 4   | Hiện trạng tài sản         | Textbox                 | Nhập văn bản           | Có            | Mô tả tình trạng hiện tại của tài sản (ví dụ: Mới, Cũ, Đã qua sử dụng)                  |
| 5   | Chủ sở hữu tài sản         | Textbox(Read-only)      | Không thao tác         | Có            | Hệ thống tự điền theo trường "họ và tên" người vay chính sản                            |
| 6   | Năm sinh                   | Date picker(read -only) | Không thao tác         | Có            | Hệ thống tự điền theo trường "ngày tháng năm sinh người vay chính, định dạng dd/mm/yyyy |
| 7   | Loại đăng ký xe            | Radio button            | Chọn 1 trong 2 giá trị | Có            | Chọn loại đăng ký xe: Có mã QR hoặc Không có mã QR, Mặc định chọn Có mã QR              |
| 8   | Nút Lưu nháp               | Button                  | Nhấn để lưu tạm thời   | Không         | Lưu thông tin tài sản bảo đảm hiện tại mà chưa chuyển bước                              |
| 9   | Nút Tiếp tục               | Button                  | Nhấn để chuyển bước    | Không         | Xác nhận thông tin và chuyển sang bước tiếp theo trong quy trình                        |
| 10  | <                          | Button                  | Click                  |               | Click để  quay trở lại bước trước đó                                                    |
| 11  | Icon trang chủ             | Button                  | Click                  |               | Click để quay trở về trang chủ                                                          |

### 1.1 Màn hình "Quét QR cà vẹt xe/ Đăng ký xe"
| **Tên trường** | **Kiểu dữ liệu**         | **Ghi chú**                                                                                                                                   |
| -------------- | ------------------------ | --------------------------------------------------------------------------------------------------------------------------------------------- |
| **Quét mã QR** | **Camera / File upload** | Quét mã QR hoặc tải ảnh mã QR từ thư viện. Hiển thị dòng text lưu ý trên khung quét QR: "Vui lòng đưa QR trên Giấy đăng ký xe vào khung hình" |
| **Bỏ qua**     | **Button**               | Cho phép bỏ qua bước quét mã QR và hiển thị màn hình nhập thông tin tài sản xe                                                                |
| **Đèn flash**  | **Button toggle**        | Bật hoặc tắt đèn flash cho camera                                                                                                             |
| **<**          | **Button**               | Trở lại trang trước                                                                                                                           |



## 2. Luồng chức năng

### 2.1 Mô tả luồng xử lý chi tiết

**Tại màn hình "Cung cấp thông tin tài sản bảo đảm"**

1. Các **trường thông tin bắt buộc:**
    - Nếu để trống → Hiện cảnh báo viền khung nhập highlight đỏ
    - Hiển thị thông báo tại trường highlight đỏ: “Bắt buộc nhập”.

2. Button **Tiếp tục** 
    - Enable nếu điền đầy đủ các trường thông tin bắt buộc
    - Nếu người dùng chọn **Có mã QR** => Click tiếp tục chuyển sang màn quét QR (Tương tự như quét QR CCCD)

---

# Bước 8: Chi tiết thông tin tài sản bảo đảm

## 1. Mô tả màn hình UI/UX 

### 1.1 Màn hình "Chi tiết thông tin tài sản bảo đảm"

| STT | Trường thông tin              | Kiểu hiển thị | Kiểu thao tác        | Bắt buộc nhập | Mô tả chi tiết                                                                                  |
| --- | ----------------------------- | ------------- | -------------------- | ------------- | ----------------------------------------------------------------------------------------------- |
|     | Thông tin chi tiết            | Lable         |                      |               |                                                                                                 |
| 1   | Biển kiểm soát                | Textbox       | Nhập văn bản         | Có            | Đọc từ QR. Nhập biển số đăng ký của phương tiện bảo đảm                                         |
| 2   | Tên tài sản                   | Textbox       | Nhập văn bản         | Có            | Đọc từ QR. Nhập tên loại tài sản (ví dụ: Xe máy, Ô tô, Nhà đất, v.v.)                           |
| 3   | Số khung                      | Textbox       | Nhập văn bản         | Có            | Đọc từ QR. Nhập số khung của phương tiện hoặc mã định danh tài sản                              |
| 4   | Số máy                        | Textbox       | Nhập văn bản         | Có            | Đọc từ QR. Nhập số máy hoặc mã số động cơ của tài sản                                           |
| 5   | Số giấy chứng nhận đăng ký xe | Textbox       | Nhập văn bản         | Có            | Đọc từ QR. Nhập số giấy chứng nhận đăng ký xe                                                   |
| 6   | Nơi cấp                       | Textbox       | Nhập văn bản         | Có            | Đọc từ QR. Nhập nơi cấp giấy chứng nhận đăng ký xe                                              |
| 7   | Ngày cấp                      | Date Picker   | Chọn ngày            | Có            | Đọc từ QR. Chọn ngày cấp giấy chứng nhận đăng ký xe . Định dạng dd/mm/yyyy                      |
| 8   | Tình trạng tài sản khi giao   | Dropdown      | Chọn                 | Có            | Gồm: Mới, cũ, đang sử dụng. Mặc định đang sử dụng                                               |
| 9   | Tổng trị giá tài sản bảo đảm  | Textbox số    | Nhập số              | Có            | Nhập tổng giá trị tài sản bảo đảm (VNĐ) ,Số tiền mặc định 0 VND, tối đa 1 tỉ, định dạng xxx,xxx |
| 10  | Nút Lưu nháp                  | Button        | Nhấn để lưu tạm thời | Không         | Lưu thông tin tạm thời của tài sản bảo đảm                                                      |
| 11  | Nút Tiếp tục                  | Button        | Nhấn để chuyển bước  | Không         | Xác nhận thông tin và chuyển sang bước tiếp theo trong quy trình                                |
| 12  | <                             | Button        | Click                |               | Click để  quay trở lại bước trước đó                                                            |
| 13  | Icon trang chủ                | Button        | Click                |               | Click để quay trở về trang chủ                                                                  |



## 2. Luồng chức năng

### 2.1 Mô tả luồng xử lý chi tiết

**Tại màn hình "Chi tiết thông tin tài sản bảo đảm"**

Nếu người dùng chọn quét mã QR nhưng không quét mà nhấn Bỏ qua => chuyển sang màn hình chi tiết tài sản bảo đảm => Các trường thông tin để trống yêu cầu người dùng nhập đủ thông tin.

Nếu quét QR thành công => Hiển thị dữ liệu và cho chỉnh sửa nếu có sai sót

1. Các **trường thông tin bắt buộc:**
    - Nếu để trống → Hiện cảnh báo viền khung nhập highlight đỏ
    - Hiển thị thông báo tại trường highlight đỏ: “Bắt buộc nhập”.

2. Button **Tiếp tục** 
    - Enable nếu điền đầy đủ các trường thông tin bắt buộc

---

# Bước 9: Cung cấp danh mục chứng từ
## 1. Mô tả màn hình UI/UX 

### 1.1 Màn hình "Cung cấp danh mục chứng từ"

| STT | Trường thông tin                         | Kiểu hiển thị | Kiểu thao tác        | Bắt buộc nhập | Mô tả chi tiết                                                                                        |
| --- | ---------------------------------------- | ------------- | -------------------- | ------------- | ----------------------------------------------------------------------------------------------------- |
| 1   | GTTT người vay chính                     | image         | View                 | Có            | Hiển thị ảnh mặt trước/ mặt sau/ or thông tin hộ chiếu người vay chính. Xem ở dạng scroll ảnh ngang   |
| 2   | GTTT người đồng vay                      | image         | View                 | Có            | Hiển thị ảnh mặt trước/ mặt sau/ or thông tin hộ chiếu người đồng chính. Xem ở dạng scroll ảnh ngang  |
| 3   | Giấy tờ tình trạng hôn nhân, mối quan hệ | File upload   | Chọn tệp để upload   | Có            | Tải lên giấy tờ chứng minh tình trạng hôn nhân hoặc quan hệ, file ảnh png, pdf  . Tối đa 5MB          |
| 4   | Giấy tờ chứng minh nơi cư trú            | File upload   | Chọn tệp để upload   | Có            | Tải lên giấy tờ chứng minh nơi cư trú của người vay hoặc đồng vay, file ảnh png, pdf . Tối đa 5MB     |
| 5   | Tờ trình thẩm định xe mô tô, gắn máy     | File upload   | Chọn tệp để upload   | Có            | Tải lên tờ trình thẩm định tài sản xe mô tô hoặc gắn máy , file ảnh png, pdf . Tối đa 5MB             |
| 6   | Giấy đăng ký xe                          | File upload   | Chọn tệp để upload   | Có            | Tải lên giấy đăng ký xe bảo đảm . file ảnh png, pdf. Tối đa 5MB                                       |
| 7   | Chứng nhận hộ kinh doanh, khác (nếu có)  | File upload   | Chọn tệp để upload   | Không         | Tải lên giấy chứng nhận hộ kinh doanh hoặc các giấy tờ khác liên quan . file ảnh png, pdf. Tối đa 5MB |
| 8   | Nút Lưu nháp                             | Button        | Nhấn để lưu tạm thời | Không         | Lưu các chứng từ đã tải lên tạm thời, chưa chuyển bước                                                |
| 9   | Nút Tiếp tục                             | Button        | Nhấn để chuyển bước  | Không         | Xác nhận các chứng từ đã tải lên và chuyển sang bước tiếp theo                                        |
| 10  | Chi tiết                                 | Button        | Nhấn để xem nội dung | Không         |                                                                                                       |
| 11  | <                                        | Button        | Click                |               | Click để  quay trở lại bước trước đó                                                                  |
| 12  | Icon trang chủ                           | Button        | Click                |               | Click để quay trở về trang chủ                                                                        |

### 1.2 Màn hình "Chọn hình thức upload tệp"

| **Tên trường**           | **Kiểu dữ liệu**                                           | **Ghi chú**                                           |
| ------------------------ | ---------------------------------------------------------- | ----------------------------------------------------- |
| **Tùy chọn tải lên ảnh** | Button (Chụp ảnh / Chọn ảnh trong thư viện / Chọn tệp tin) | Chụp ảnh, tải ảnh từ thư viện hoặc chọn tệp ti từ máy |
| **Huỷ bỏ**               | Button                                                     | Để huỷ bỏ thao tác upload tệp                         |

### 1.3 Màn hình "Xem lại ảnh và xoá"

| **Tên trường** | **Kiểu dữ liệu** | **Ghi chú**                                |
| -------------- | ---------------- | ------------------------------------------ |
| **Chọn ảnh**   | Check box        | Nhấn tích chọn để xoá ảnh                  |
| **Chọn all**   | Button           | Nhấn chọn all để xoá tất cả ảnh đã tải lên |
| **Xoá**        | Button           | Nhấn xoá ảnh                               |
| **<**          | Button           | Nhấn để quay trở lại màn chứng từ          |


## 2. Luồng chức năng

### 2.1 Mô tả luồng xử lý chi tiết

**Tại màn hình "Cung cấp danh mục chứng từ"**

1. Nếu người dùng chọn vào file upload =>  Hiển thị màn hình **"Chọn hình thức upload tệp"**
2. Từ màn hình **"Chọn hình thức upload tệp"** 
     - chọn **Chụp ảnh** => hiển thị luồng cho phép chụp nhiều ảnh (tối đa ??? ảnh) => Hiển thị thành **list ảnh**, tối đa 3 ảnh hiển thị trên màn hình. Nếu lớn hơn 3 ảnh hiển thị thành + số ảnh
     - Chọn chọn ảnh trong thư viện => hiển thị màn hình chọn ảnh trong máy (tối đa ??? ảnh)  => Hiển thị thành **list ảnh**, tối đa 3 ảnh hiển thị trên màn hình. Nếu lớn hơn 3 ảnh hiển thị thành + số ảnh
     - Chọn **Tải tệp tin** => tối đa 5MB => click dấu X để xoá file đã tải
3. Các **trường thông tin bắt buộc:**
    - Nếu để trống → Hiện cảnh báo viền khung nhập highlight đỏ
    - Hiển thị thông báo tại trường highlight đỏ: “Bắt buộc nhập”.

4. Button **Tiếp tục** 
    - Enable nếu điền đầy đủ các trường thông tin bắt buộc

**Tại màn hình "Xem lại ảnh và xoá"**
   1. Sau khi tải ảnh lên, Tại màn "Cung cấp danh mục chứng từ" => Click vào **List ảnh** => Hiển thị màn hình **"Xem lại ảnh và xoá"**
    - Tích chọn 1, nhiều/ all ảnh => chọn xoá để clear dữ liệu ảnh đã chụp/ tải lên

---

# Bước 10: Xác nhận thông tin khoản vay

## 1. Mô tả màn hình UI/UX 

### 1.1 Màn hình "Xác nhận thông tin khoản vay"

| STT | Trường thông tin                                                                                                         | Kiểu hiển thị | Kiểu thao tác | Mô tả chi tiết |
| --- | ------------------------------------------------------------------------------------------------------------------------ | ------------- | ------------- | -------------- |
|     | **Thông tin người vay chính**                                                                                            | Lable         |               |                |
| 1   | Họ và tên                                                                                                                | Lable         | read-only     |                |
| 2   | Số giấy tờ                                                                                                               | Lable         | read-only     |                |
| 3   | Ngày cấp                                                                                                                 | Lable         | read-only     |                |
| 4   | Ngày hết hạn                                                                                                             | Lable         | read-only     |                |
| 5   | Nơi cấp                                                                                                                  | Lable         | read-only     |                |
| 6   | Ngày  sinh                                                                                                               | Lable         | read-only     |                |
| 7   | Giới tính                                                                                                                | Lable         | read-only     |                |
| 8   | Hộ khẩu thường trú                                                                                                       | Lable         | read-only     |                |
| 9   | Địa chỉ hiện tại                                                                                                         | Lable         | read-only     |                |
| 10  | Tình trạng hôn nhân                                                                                                      | Lable         | read-only     |                |
| 11  | Số điện thoại                                                                                                            | Lable         | read-only     |                |
|     | **Đề nghị vay vốn**                                                                                                      | Lable         |               |                |
| 1   | Hình thức vay vốn                                                                                                        | Lable         | read-only     |                |
| 2   | Vốn tự có                                                                                                                | Lable         | read-only     |                |
| 3   | Số tiền đề nghị vay                                                                                                      | Lable         | read-only     |                |
| 4   | Thời hạn vay                                                                                                             | Lable         | read-only     |                |
| 5   | Tổng nhu cầu                                                                                                             | Lable         | read-only     |                |
| 6   | CN/PGD                                                                                                                   | Lable         | read-only     |                |
| 7   | Phương thức vay                                                                                                          | Lable         | read-only     |                |
| 8   | Mục đích sử dụng vốn                                                                                                     | Lable         | read-only     |                |
| 9   | Hình thức trả nợ                                                                                                         | Lable         | read-only     |                |
| 10  | Phương thức giải ngân                                                                                                    | Lable         | read-only     |                |
|     | **Tình hình tài chính**                                                                                                  | Lable         |               |                |
| 1   | Nguồn thu                                                                                                                | Lable         | read-only     |                |
| 2   | Doanh số bình quân/Ngày                                                                                                  | Lable         | read-only     |                |
| 3   | Thu nhập bình quân /Ngày                                                                                                 | Lable         | read-only     |                |
| 4   | Tỉnh/ Thành phố                                                                                                          | Lable         | read-only     |                |
| 5   | Địa chỉ cụ thể                                                                                                           | Lable         | read-only     |                |
|     | **Tài sản bảo đảm**                                                                                                      | Lable         |               |                |
| 1   | Loại tài sản                                                                                                             | Lable         | read-only     |                |
| 2   | Giá trị tài sản                                                                                                          | Lable         | read-only     |                |
| 3   | Giá trị tài sản( bằng chữ)                                                                                               | Lable         | read-only     |                |
| 4   | Hiện trạng tài sản                                                                                                       | Lable         | read-only     |                |
| 5   | Chủ sở hữu tài sản                                                                                                       | Lable         | read-only     |                |
| 6   | Năm sinh                                                                                                                 | Lable         | read-only     |                |
| 7   | Loại đăng ký xe                                                                                                          | Lable         | read-only     |                |
| 8   | Biển kiểm soát                                                                                                           | Lable         | read-only     |                |
| 9   | Tên tài sản                                                                                                              | Lable         | read-only     |                |
| 10  | Số khung                                                                                                                 | Lable         | read-only     |                |
| 11  | Số máy                                                                                                                   | Lable         | read-only     |                |
| 12  | Số chứng nhận đăng ký xe                                                                                                 | Lable         | read-only     |                |
| 13  | Nơi cấp                                                                                                                  | Lable         | read-only     |                |
| 14  | Ngày cấp                                                                                                                 | Lable         | read-only     |                |
| 15  | Tổng giá trị tài sản bảo đảm                                                                                             | Lable         | read-only     |                |
| 16  | Tôi xác nhận các nội dung khách hàng đã nêu trên trùng khớp và phù hợp; khách hàng có đủ điều kiện vay vốn và trả nợ vay | Checkbox      | Click         |                |
| 17  | Chỉnh sửa                                                                                                                | Button        | Click         |                |
| 18  | Xác nhận                                                                                                                 | Button        | Click         |                |
| 19  | <                                                                                                                        | Button        | Click         |                |
| 20  | Trang chủ                                                                                                                | Button        | Click         |                |


## 2. Luồng chức năng

### 2.1 Mô tả luồng xử lý chi tiết

**Tại màn hình "Xác nhận thông tin khoản vay"**
1. Button **Xác nhận** 
    - Click xác nhạn để chuyển sang màn khởi tạo hồ sơ thành công

---

# Bước 11: Màn hình khởi tạo khoản vay thành công

## 1. Mô tả màn hình UI/UX 

### 1.1 Màn hình Màn hình khởi tạo khoản vay thành công
| STT | Trường thông tin               | Kiểu hiển thị | Kiểu thao tác | Mô tả chi tiết                                     |
| --- | ------------------------------ | ------------- | ------------- | -------------------------------------------------- |
| 1   | Khởi tạo khoản vay thành công  | Lable         |               |                                                    |
| 2   | Ngày giờ                       | Lable         | read-only     | Định dạng: dd/mm/yyyy hh:mm:ss                     |
|     | **Thông tin hồ sơ   **         | Lable         | read-only     |                                                    |
| 3   | Họ tên                         | Lable         | read-only     |                                                    |
| 4   | Khoản vay                      | Lable         | read-only     |                                                    |
| 5   | Thời hạn vay                   | Lable         | read-only     |                                                    |
| 6   | Về trang chủ                   | Button        | Click         |                                                    |
| 7   | Tạo khoản vay KH mới           | Button        | Click         | Click để tạo hồ sơ mới                             |
| 8   | Tạo thêm khoản vay KH hiện hữu | Button        | Click         | Click để tạo thêm khoản vay với khách hàng vừa tạo |


