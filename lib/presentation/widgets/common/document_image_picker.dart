import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:sales_app/core/theme/app_color.dart';
import 'package:sales_app/core/constants/app_dimens.dart';
import 'package:sales_app/core/services/image_picker_service.dart';
import 'package:sales_app/core/enums/document_side.dart';
import '../../screens/document/capture_document_screen.dart';
import 'photo_source_picker.dart';

// Custom painter để vẽ viền nét đứt
class DashedBorderPainter extends CustomPainter {
  final Color color;
  final double strokeWidth;
  final double dashWidth;
  final double dashSpace;
  final BorderRadius borderRadius;

  DashedBorderPainter({
    required this.color,
    this.strokeWidth = 1.0,
    this.dashWidth = 5.0,
    this.dashSpace = 3.0,
    required this.borderRadius,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke;

    final path = Path()
      ..addRRect(RRect.fromRectAndCorners(
        Rect.fromLTWH(0, 0, size.width, size.height),
        topLeft: borderRadius.topLeft,
        topRight: borderRadius.topRight,
        bottomLeft: borderRadius.bottomLeft,
        bottomRight: borderRadius.bottomRight,
      ));

    final dashPath = _createDashedPath(path, dashWidth, dashSpace);
    canvas.drawPath(dashPath, paint);
  }

  Path _createDashedPath(Path source, double dashWidth, double dashSpace) {
    final Path dashedPath = Path();
    final pathMetrics = source.computeMetrics();

    for (final pathMetric in pathMetrics) {
      double distance = 0.0;
      bool draw = true;

      while (distance < pathMetric.length) {
        final double length = draw ? dashWidth : dashSpace;
        if (distance + length > pathMetric.length) {
          if (draw) {
            dashedPath.addPath(
              pathMetric.extractPath(distance, pathMetric.length),
              Offset.zero,
            );
          }
          break;
        } else {
          if (draw) {
            dashedPath.addPath(
              pathMetric.extractPath(distance, distance + length),
              Offset.zero,
            );
          }
          distance += length;
          draw = !draw;
        }
      }
    }
    return dashedPath;
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}

class DocumentImagePicker extends StatelessWidget {
  final String label;
  final String? imagePath;
  final Function(String path) onImageSelected;

  const DocumentImagePicker({
    super.key,
    required this.label,
    required this.onImageSelected,
    this.imagePath,
  });

  Future<void> _handleTap(BuildContext context) async {
    final imagePicker = GetIt.I<ImagePickerService>();
    
    await showCupertinoPhotoSourcePicker(
      context: context,
      onTakePhoto: () async {
        if (context.mounted) {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => CaptureDocumentScreen(
                title: 'Chụp $label',
                side: label.toLowerCase().contains('trước') 
                    ? DocumentSide.front 
                    : DocumentSide.back,
                onImageCaptured: (image) => onImageSelected(image.path),
              ),
            ),
          );
        }
      },
      onPickFromGallery: () async {
        final path = await imagePicker.pickFromGallery(
          maxWidth: 1920,
          maxHeight: 1080,
          quality: 90,
        );
        if (path != null) {
          onImageSelected(path);
        }
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: theme.textTheme.bodyMedium?.copyWith(
            color: AppColors.black,
            fontWeight: FontWeight.w500,
          ),
        ),
        AppDimens.h8,
        InkWell(
          onTap: () => _handleTap(context),
          borderRadius: AppDimens.borderRadius12,
          child: CustomPaint(
            painter: DashedBorderPainter(
              color: AppColors.primaryColor.withValues(alpha: 0.3),
              strokeWidth: 1.5,
              dashWidth: 8.0,
              dashSpace: 6.0,
              borderRadius: AppDimens.borderRadius12,
            ),
            child: Container(
              height: 180,
              width: double.infinity,
              decoration: BoxDecoration(
                color: AppColors.primaryColor.withValues(alpha: 0.05),
                borderRadius: AppDimens.borderRadius12,
              ),
              child: imagePath != null
                  ? ClipRRect(
                      borderRadius: AppDimens.borderRadius12,
                      child: Image.file(
                        File(imagePath!),
                        fit: BoxFit.cover,
                      ),
                    )
                  : Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.camera_alt_outlined,
                          color: AppColors.primaryColor.withValues(alpha: 0.7),
                          size: AppDimens.iconXL,
                        ),
                        AppDimens.h16,
                        Text(
                          'Chạm để chọn ảnh',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: AppColors.primaryColor.withValues(alpha: 0.7),
                          ),
                        ),
                      ],
                    ),
            ),
          ),
        ),
      ],
    );
  }
}
