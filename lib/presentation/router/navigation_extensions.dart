import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

// Route constants from core
import '../../core/router/app_routes.dart';
import '../../core/utils/app_logger.dart';

/// Extension methods for easy navigation
/// Located in presentation layer for Clean Architecture compliance
///
/// Clean Architecture compliance:
/// - Presentation layer can use Core layer utilities (app_routes, app_logger)
/// - Navigation logic stays in presentation layer
/// - No dependency violations
extension NavigationExtensions on BuildContext {
  // Auth Navigation Methods (Push - with back button)

  /// Push to select account type screen
  void goToSelectAccountType() {
    AppLogger.event('Navigation: Pushing to Select Account Type');
    push(AppRoutes.selectAccountType);
  }

  /// Push to personal info confirmation screen
  void goToPersonalInfoConfirmation() {
    AppLogger.event('Navigation: Pushing to Personal Info Confirmation');
    push(AppRoutes.personalInfoConfirmation);
  }

  /// Push to CTV policy screen
  void goToCtvPolicy() {
    AppLogger.event('Navigation: Pushing to CTV Policy');
    push(AppRoutes.ctvPolicy);
  }

  /// Push to KYC ID guide screen
  void goToKycIdGuide() {
    AppLogger.event('Navigation: Pushing to KYC ID Guide');
    push(AppRoutes.kycIdGuide);
  }

  /// Push to registration success screen
  void goToRegistrationSuccess() {
    AppLogger.event('Navigation: Pushing to Registration Success');
    push(AppRoutes.registrationSuccess);
  }

  // Document Navigation Methods (Push - with back button)

  /// Push to capture document screen
  void goToCaptureDocument() {
    AppLogger.event('Navigation: Pushing to Capture Document');
    push(AppRoutes.captureDocument);
  }

  /// Push to preview document screen
  void goToPreviewDocument() {
    AppLogger.event('Navigation: Pushing to Preview Document');
    push(AppRoutes.previewDocument);
  }

  /// Push preview document screen with parameters
  void pushPreviewDocumentWithParams({
    required dynamic image,
    required dynamic side,
    required dynamic onAccept,
  }) {
    AppLogger.event('Navigation: Pushing Preview Document with params');
    push(
      AppRoutes.previewDocument,
      extra: {
        'image': image,
        'side': side,
        'onAccept': onAccept,
      },
    );
  }

  // Identity Navigation Methods (Push - with back button)

  /// Push to identity upload screen
  void goToIdentityUpload() {
    AppLogger.event('Navigation: Pushing to Identity Upload');
    push(AppRoutes.identityUpload);
  }

  // Replace Navigation Methods (No back button)

  /// Navigate to login screen (replace current route)
  void goToLogin() {
    AppLogger.event('Navigation: Going to Login (replace)');
    go(AppRoutes.login);
  }

  /// Navigate to home screen (replace current route - for login flow)
  void goToHome() {
    AppLogger.event('Navigation: Going to Home (replace)');
    go(AppRoutes.home);
  }

  /// Replace with login (for logout or session expired)
  void replaceWithLogin() {
    AppLogger.event('Navigation: Replacing with Login');
    go(AppRoutes.login);
  }

  /// Replace with home (for successful login)
  void replaceWithHome() {
    AppLogger.event('Navigation: Replacing with Home');
    go(AppRoutes.home);
  }

  // Push Navigation Methods (Alternative - with back button)

  /// Push to home screen (for navigation from other screens)
  void pushToHome() {
    AppLogger.event('Navigation: Pushing to Home');
    push(AppRoutes.home);
  }

  // Demo Navigation

  /// Push to navigation demo screen
  void goToNavigationDemo() {
    AppLogger.event('Navigation: Pushing to Navigation Demo');
    push(AppRoutes.navigationDemo);
  }

  /// Push to UI demo screen
  void goToUiDemo() {
    AppLogger.event('Navigation: Pushing to UI Demo');
    push(AppRoutes.uiDemo);
  }

  // Utility Navigation Methods

  /// Go back to previous screen
  void goBack() {
    AppLogger.event('Navigation: Going back');
    if (canPop()) {
      pop();
    } else {
      // If can't pop, go to home or login
      goToLogin();
    }
  }

  /// Replace current route with new route (use go())
  void replaceWith(String route) {
    AppLogger.event('Navigation: Replacing with $route');
    go(route);
  }

  /// Clear navigation stack and go to route (use go())
  void clearAndGoTo(String route) {
    AppLogger.event('Navigation: Clearing stack and going to $route');
    go(route);
  }

  void goToQRScan() {
    AppLogger.event('Navigation: Pushing to Select Account Type');
    push(AppRoutes.qrScan);
  }
}
