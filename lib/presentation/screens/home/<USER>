import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../../../core/theme/app_color.dart';
import '../../../core/constants/app_dimens.dart';
import 'widgets/modern_search_bar.dart';
import 'widgets/modern_quick_actions.dart';
import 'widgets/modern_dashboard_cards.dart';
import 'widgets/modern_ctv_overview.dart';
import 'widgets/modern_quick_reports.dart';
import 'widgets/modern_product_info.dart';
import 'widgets/modern_recent_viewed.dart';
import 'widgets/modern_user_guide.dart';

// Navigation imports (Clean Architecture compliant)
import '../../router/navigation_extensions.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  // Mock user data
  final String userName = "Nguyễn <PERSON>ăn <PERSON>";
  final String userRole = "Cộng tác viên";
  final int notificationCount = 5;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: AppDimens.animationVerySlow),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppColors.primaryColor,
              AppColors.primaryColor.withValues(alpha: 0.8),
              AppColors.surfaceColor,
            ],
            stops: const [0.0, 0.3, 1.0],
          ),
        ),
        child: SafeArea(
          child: RefreshIndicator(
            onRefresh: _refreshData,
            color: AppColors.primaryColor,
            child: CustomScrollView(
              physics: const BouncingScrollPhysics(),
              slivers: [
                // Modern Header với Avatar và Greeting
                _buildModernHeader(),

                // Search Bar Section
                SliverToBoxAdapter(
                  child: FadeTransition(
                    opacity: _fadeAnimation,
                    child: const ModernSearchBar(),
                  ),
                ),

                // Quick Actions Section
                SliverToBoxAdapter(
                  child: FadeTransition(
                    opacity: _fadeAnimation,
                    child: ModernQuickActions(
                      notificationCount: notificationCount,
                    ),
                  ),
                ),

                // Dashboard Cards
                SliverToBoxAdapter(
                  child: FadeTransition(
                    opacity: _fadeAnimation,
                    child: const ModernDashboardCards(),
                  ),
                ),

                // CTV Overview Section (Tổng quan) - Simple and Clean Design
                SliverToBoxAdapter(
                  child: FadeTransition(
                    opacity: _fadeAnimation,
                    child: const ModernCtvOverview(),
                  ),
                ),

                // Quick Reports Section (Báo cáo nhanh)
                SliverToBoxAdapter(
                  child: FadeTransition(
                    opacity: _fadeAnimation,
                    child: const ModernQuickReports(),
                  ),
                ),

                // Product Info Section (Thông tin sản phẩm)
                SliverToBoxAdapter(
                  child: FadeTransition(
                    opacity: _fadeAnimation,
                    child: const ModernProductInfo(),
                  ),
                ),

                // Recent Viewed Section (Đã xem gần đây)
                SliverToBoxAdapter(
                  child: FadeTransition(
                    opacity: _fadeAnimation,
                    child: const ModernRecentViewed(),
                  ),
                ),

                // User Guide Section (Hướng dẫn sử dụng)
                SliverToBoxAdapter(
                  child: FadeTransition(
                    opacity: _fadeAnimation,
                    child: const ModernUserGuide(),
                  ),
                ),

                // Bottom spacing
                SliverToBoxAdapter(
                  child: AppDimens.h32,
                ),
              ],
            ),
          ),
        ),
      ),
      // Debug: Demo FAB (chỉ hiển thị trong debug mode)
      floatingActionButton: kDebugMode ? _buildDebugFab() : null,
    );
  }

  Widget _buildModernHeader() {
    return SliverToBoxAdapter(
      child: Container(
        padding: AppDimens.paddingAllLg,
        child: Row(
          children: [
            // Avatar
            Container(
              width: AppDimens.containerLg,
              height: AppDimens.containerLg,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                gradient: const LinearGradient(
                  colors: [
                    AppColors.secondaryColor,
                    AppColors.buttonColor,
                  ],
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: AppDimens.alphaMedium),
                    blurRadius: AppDimens.spacingSM,
                    offset: Offset(0, AppDimens.spacingXS),
                  ),
                ],
              ),
              child: Icon(
                Icons.person,
                size: AppDimens.iconLG,
                color: AppColors.textWhite,
              ),
            ),

            AppDimens.w16,

            // User Info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _getGreeting(),
                    style: TextStyle(
                      fontSize: AppDimens.fontMD,
                      color: AppColors.textWhite.withValues(alpha: AppDimens.alphaNearOpaque),
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                  Text(
                    userName,
                    style: TextStyle(
                      fontSize: AppDimens.fontXL,
                      fontWeight: FontWeight.bold,
                      color: AppColors.textWhite,
                    ),
                  ),
                  Text(
                    userRole,
                    style: TextStyle(
                      fontSize: AppDimens.fontSM,
                      color: AppColors.textWhite.withValues(alpha: AppDimens.alphaVeryHigh),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),

            // Notification Bell
            Container(
              width: AppDimens.containerMd,
              height: AppDimens.containerMd,
              decoration: BoxDecoration(
                color: AppColors.textWhite.withValues(alpha: AppDimens.alphaMedium),
                borderRadius: AppDimens.borderRadius12,
              ),
              child: Stack(
                children: [
                  Center(
                    child: Icon(
                      Icons.notifications_outlined,
                      color: AppColors.textWhite,
                      size: AppDimens.iconMD,
                    ),
                  ),
                  if (notificationCount > 0)
                    Positioned(
                      right: AppDimens.spacingSM,
                      top: AppDimens.spacingSM,
                      child: Container(
                        padding: EdgeInsets.all(AppDimens.spacingXS),
                        decoration: const BoxDecoration(
                          color: AppColors.error,
                          shape: BoxShape.circle,
                        ),
                        constraints: BoxConstraints(
                          minWidth: AppDimens.badgeSize,
                          minHeight: AppDimens.badgeSize,
                        ),
                        child: Text(
                          notificationCount > AppDimens.maxNotificationDisplay ? '${AppDimens.maxNotificationDisplay}+' : '$notificationCount',
                          style: TextStyle(
                            color: AppColors.textWhite,
                            fontSize: AppDimens.fontXS,
                            fontWeight: FontWeight.bold,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _getGreeting() {
    final hour = DateTime.now().hour;
    if (hour < 12) {
      return 'Chào buổi sáng 👋';
    } else if (hour < 17) {
      return 'Chào buổi chiều 👋';
    } else {
      return 'Chào buổi tối 👋';
    }
  }

  Future<void> _refreshData() async {
    await Future.delayed(const Duration(milliseconds: AppDimens.animationVerySlow * 2));
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('Dữ liệu đã được cập nhật'),
          backgroundColor: AppColors.success,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: AppDimens.borderRadius8,
          ),
        ),
      );
    }
  }

  /// Build debug FAB với menu để chọn demo screen
  Widget _buildDebugFab() {
    return FloatingActionButton(
      onPressed: _showDebugMenu,
      backgroundColor: AppColors.primaryColor,
      child: const Icon(Icons.bug_report, color: Colors.white),
    );
  }

  /// Hiển thị menu debug với các options
  void _showDebugMenu() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: AppColors.backgroundColor,
          borderRadius: BorderRadius.only(
            topLeft: AppDimens.borderRadius16.topLeft,
            topRight: AppDimens.borderRadius16.topRight,
          ),
        ),
        child: SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header
              Container(
                padding: AppDimens.paddingAllLg,
                child: Row(
                  children: [
                    Icon(
                      Icons.bug_report,
                      color: AppColors.primaryColor,
                      size: AppDimens.iconMD,
                    ),
                    AppDimens.w12,
                    Text(
                      'Debug Menu',
                      style: TextStyle(
                        fontSize: AppDimens.fontLG,
                        fontWeight: FontWeight.bold,
                        color: AppColors.textPrimary,
                      ),
                    ),
                    const Spacer(),
                    IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: Icon(
                        Icons.close,
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),

              // Divider
              Divider(color: AppColors.grey300, height: 1),

              // Menu Items
              _buildDebugMenuItem(
                icon: Icons.navigation,
                title: 'Navigation Demo',
                subtitle: 'Test navigation patterns',
                onTap: () {
                  Navigator.pop(context);
                  context.goToNavigationDemo();
                },
              ),

              _buildDebugMenuItem(
                icon: Icons.palette,
                title: 'UI Components Demo',
                subtitle: 'Showcase common & shared widgets',
                onTap: () {
                  Navigator.pop(context);
                  context.goToUiDemo();
                },
              ),

              AppDimens.h16,
            ],
          ),
        ),
      ),
    );
  }

  /// Build debug menu item
  Widget _buildDebugMenuItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Container(
        width: AppDimens.containerMd,
        height: AppDimens.containerMd,
        decoration: BoxDecoration(
          color: AppColors.primaryColor.withValues(alpha: 0.1),
          borderRadius: AppDimens.borderRadius8,
        ),
        child: Icon(
          icon,
          color: AppColors.primaryColor,
          size: AppDimens.iconMD,
        ),
      ),
      title: Text(
        title,
        style: TextStyle(
          fontSize: AppDimens.fontMD,
          fontWeight: FontWeight.w600,
          color: AppColors.textPrimary,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          fontSize: AppDimens.fontSM,
          color: AppColors.textSecondary,
        ),
      ),
      onTap: onTap,
      contentPadding: AppDimens.paddingHorizontalLg,
    );
  }
}